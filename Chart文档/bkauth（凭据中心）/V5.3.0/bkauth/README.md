# BKAuth

BKAuth 是由蓝鲸 PaaS 平台开发的认证服务，主要提供AppSecret集中认证和OAuth2.0用户认证授权等功能。本文档内容为如何在 Kubernetes 集群上部署 BKAuth 服务。

## 部署

### 准备服务依赖

开始部署前，请准备好一套 Kubernetes 集群（版本 1.12 或更高），并安装 Helm 命令行工具（3.0 或更高版本）。

> 注：如使用 BCS 容器服务部署，可用 BCS 的图形化 Helm 功能替代 Helm 命令行。

#### 数据存储

以下为 BKAuth 必须使用的数据存储服务：

- MySQL：用于存储关系数据，要求版本为 `5.7` 或更高；
- Redis：用于保存缓存数据，提升AppSecret和AccessToken认证的性能；

> 注：你可以选择自己搭建，或者直接从云计算厂商处购买这些服务，只要能保证从集群内能正常访问即可。

**注意，内置的存储依赖仅为方便开发联调而提供，默认开启，生产环境务必关闭。**

| 存储    | 控制变量        | 类型 | 默认值 |
| ------- | --------------- | ---- | ------ |
| mariadb | mariadb.enabled | bool | true   |
| redis   | reids.enabled   | bool | true   |

#### 其他服务

要正常运行 BKAUTH，除了准备核心的数据存储外，还需要用到一些蓝鲸体系内的其他服务，它们是：

- 蓝鲸统一登录：依赖其校验登录态
- 蓝鲸工作台 open_paas 库：依赖同步appcode、secret，后续版本将会去除依赖
- 蓝鲸 BCS 容器服务（版本 1.3.25 以上）：用于简化部署
  - 如缺失，对程序功能没有任何影响

准备好依赖服务后，下一步是编写 `values.yaml` 配置文件。

### 准备 `values.yaml`

#### 1. 配置镜像地址

编写配置文件的第一步，是将 `image.registry` 配置为你所使用的镜像源地址。然后，再确认每个模块所使用镜像 tag 是否正确。

###### `values.yaml` 配置示例：

```yaml
image:
  # 请保证服务相关的容器镜像已上传至该 registry 中
  registry: "mirrors.tencent.com"
  repository: blueking/bkauth
  tag: v0.0.3
migration:
  images:
    busybox:
      repository: blueking/busybox
      tag: 1.34.0
    k8sWaitFor:
      repository: blueking/k8s-wait-for
      tag: v1.5.1
    sqlMigrate:
      repository: blueking/sql-migrate
      tag: 1.0.0
```

> 注：假如服务镜像需凭证才能拉取。请将对应密钥名称写入配置文件中，详细请查看 `global.imagePullSecrets` 配置项说明。


#### 2. 配置数据加密密钥

BKAuth 服务使用对称加密算法保障敏感配置与服务数据安全。要启用加密功能，你首先得创建一个独一无二的密钥。该密钥内容为 **长度为 32 的字符串（由大小写和数字组成）**。

在 Linux 系统中，你可执行以下命令生成一个随机密钥：

```bash
$ tr -dc A-Za-z0-9 </dev/urandom | head -c 32
```

或者调用 Python 命令：

```bash
$ python -c 'import random, string, base64; s = "".join(random.choice(string.ascii_letters + string.digits) for _ in range(32)); print(s)'
```

拿到密钥后，下一步是将其放入 `appSecretEncryptedKey` 配置项中。

注意事项:

- 密钥一旦生成并配置好以后，不可修改，否则会导致数据异常；
- 为了你的数据安全，请不要将密钥泄露给其他人。
- 加密DB里存储的AppSecret，一旦设置后，需要单独记录起来，否则丢失后无法解密AppSecret

###### `values.yaml` 配置示例：

```yaml
appSecretEncryptedKey: "b3BmRmpwYWNoZH..."
```

#### 3. 初始化与配置数据存储

准备好服务所依赖的存储后，必须完成以下初始化操作：

**MySQL**
使用内置存储可跳过1步骤。

1. 创建普通用户 `bkauth`；
2. 对于已有的 PaaS2 服务数据库 `open_paas`，授予用户 `bkauth` 读取数据的权限；

**Redis**

1. 使用 redis-cli 命令测试 Redis 可正常连接使用

##### 填写数据存储配置

```yaml
## External Database Configuration
## All of these values are only used if `mariadb.enabled=false`
##
externalDatabase:
  default:
    ## @param externalDatabase.default.host External Database server host
    ##
    host: localhost
    ## @param externalDatabase.default.port External Database server port
    ##
    port: 3306
    ## @param externalDatabase.default.user External Database username
    ##
    user: bkauth
    ## @param externalDatabase.default.password External Database user password
    ##
    password: ""
    ## @param externalDatabase.default.database External Database database name
    ##
    database: bkauth
  openPaas:
    ## @param externalDatabase.openPaas.enabled 开启时, 优先使用外部db, false时使用mariadb
    enabled: true

    ## @param externalDatabase.openPaas.host External Database server host
    ##
    host: bk-panel-mariadb
    ## @param externalDatabase.openPaas.port External Database server port
    ##
    port: 3306
    ## @param externalDatabase.openPaas.user External Database username
    ##
    user: bk_panel
    ## @param externalDatabase.openPaas.password External Database user password
    ##
    password: "root"
    ## @param externalDatabase.openPaas.database External Database database name
    ##
    database: open_paas

## If the Redis(TM) included in the chart is disabled, Harbor will use below
## Redis(TM) parameters to connect to an external Redis(TM) server.
## Support for both Redis(TM) and Redis+Sentinel(TM)
##
externalRedis:
  default:
    ## Redis(TM) host
    ##
    host: localhost
    ## Redis(TM) port number
    ##
    port: 6379
    ## Redis(TM) password
    ##
    password: "bkauth"
    ## Redis(TM) db name
    ##
    db: 0
```


#### 4. 配置 BKAuth

为了让 BKAuth 正常工作，有一些专有配置需要设置。

```yaml
## 是否开启调试日志
debug: false

## 启用sentry
sentryDsn: ""
```

#### 5. 配置服务访问域名

```yaml
## Configure the ingress resource that allows you to access the bkauth installation
## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/
##
ingress:
  ## @param ingress.enabled Enable ingress record generation for bkauth
  ##
  enabled: true

  ## @param ingress.hostname Default host for the ingress record
  ##
  hostname: bkauth.example.com
```


### 安装Chart

完成 `values.yaml` 的所有准备工作后，要安装 BKAuth，你必须先添加一个有效的 Helm repo 仓库。

```shell
## 请将 `<HELM_REPO_URL>` 替换为本 Chart 所在的 Helm 仓库地址
$ helm repo add bkee <HELM_REPO_URL>
```

添加仓库成功后，执行以下命令，在集群内安装名为 `bkauth` 的 Helm release（使用默认项目配置）：

```shell
## 使用 --wait 参数，因服务成功启动后，需注册默认网关
$ helm install bkauth blueking/bkauth --wait -f values.yaml
```

> 注意：使用 BCS 部署时，请勾选 **Helm命令行参数** 中的 `wait`，确保BKAuth按照成功。

上述命令将使用默认配置在 Kubernetes 集群中部署 bkauth, 并输出访问指引。

### 卸载Chart

使用以下命令卸载`bkauth`:

```shell
$ helm uninstall bkauth

# 如果使用内置的mariadb/redis, 需要删除持久存储的pvc, 可以使用以下命令
kubectl delete deploy,sts,job,pod,svc,ingress,secret,cm,sa,role,rolebinding,pvc -l app.kubernetes.io/instance=bkauth
```

上述命令将移除所有与 bkauth 相关的 Kubernetes 组件，并删除 release。

### 配置说明

以下为可配置的参数列表以及默认值

| 参数                                           | 类型   | 默认值                                    | 描述                    |
|----------------------------------------------|------|----------------------------------------|-----------------------|
| enabled                                | bool | true                                   | 是否启用BKAuth            |
| image.registry                         | str  | mirrors.tencent.com                    | 镜像源地址                 |
| image.repository                       | str  | blueking/bkauth                         | BKAuth 服务镜像地址         |
| image.tag                              | str  | v0.0.3                                 | BKAuth 服务镜像标签         |
| image.pullPolicy                       | str  | IfNotPresent                           | 镜像拉取策略                |
| ingress.hostname                       | str  | bkauth.example.com                  | BKAuth访问域名              |
| replicaCount                           | int  | 1                                      | 默认进程副本数               |
| fullnameOverride                       | str  | bkauth                                 | 覆盖默认名称                |
| externalDatabase.default.host          | str  | localhost                              | 默认的数据库地址              |
| externalDatabase.default.port          | int  | 3306                                   | 默认的数据库端口              |
| externalDatabase.default.user          | str  | bkauth                                  | 默认的数据库用户              |
| externalDatabase.default.password      | str  |                                        | 默认的数据库密码              |
| externalDatabase.default.database      | str  | bkauth                                  | 默认的数据库密码              |
| externalDatabase.openPaas.enabled      | bool | true                                   | 是否启用外部open_paas数据库    |
| externalDatabase.openPaas.host         | str  | bk-panel-mariadb                       | openPaas的数据库地址        |
| externalDatabase.openPaas.port         | int  | 3306                                   | openPaas的数据库端口        |
| externalDatabase.openPaas.user         | str  | bk_panel                               | openPaas的数据库用户        |
| externalDatabase.openPaas.password     | str  | root                                   | openPaas的数据库密码        |
| externalDatabase.openPaas.database     | str  | open_paas                              | openPaas的数据库密码        |
| externalRedis.default.host             | str  | localhost                              | 默认的 Redis 地址          |
| externalRedis.default.port             | int  | 6379                                   | 默认的 Redis 端口          |
| externalRedis.default.password         | str  |                                        | 默认的 Redis 密码          |
| externalRedis.default.db               | int  | 0                                      | 默认的 Redis 实例          |
| debug                                  | bool | false                                  | 开启调试日志                |
| sentryDsn                              | str  |                                        | sentry dsn            |
| appSecretEncryptedKey                | str  |                                        | DB里加密appSecret的密钥 |


#### 如何修改配置项

在安装 Chart 时，你可以通过 `--set key=value[,key=value]` 的方式，在命令参数里修改各配置项。例如:

```shell
$ helm install bkauth blueking/bkauth --wait \
  --set mariadb.enabled=false
```

此外，你也可以把所有配置项写在 YAML 文件（常被称为 Helm values 文件）里，通过 `-f` 指定该文件来使用特定配置项：

```shell
$ helm install bkauth blueking/bkauth --wait -f values.yaml
```

执行 `helm show values`，你可以查看 Chart 的所有默认配置：

```shell
## 查看默认配置
$ helm show values blueking/bkauth

## 保存默认配置到文件 values.yaml
$ helm show values blueking/bkauth > values.yaml
```