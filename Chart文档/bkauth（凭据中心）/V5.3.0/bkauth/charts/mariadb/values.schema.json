{"$schema": "http://json-schema.org/schema#", "type": "object", "properties": {"architecture": {"type": "string", "title": "MariaDB architecture", "form": true, "description": "Allowed values: `standalone` or `replication`"}, "auth": {"type": "object", "title": "Authentication configuration", "form": true, "properties": {"rootPassword": {"type": "string", "title": "MariaDB root password", "form": true, "description": "Defaults to a random 10-character alphanumeric string if not set"}, "database": {"type": "string", "title": "MariaDB custom database", "description": "Name of the custom database to be created during the 1st initialization of MariaDB", "form": true}, "username": {"type": "string", "title": "MariaDB custom user", "description": "Name of the custom user to be created during the 1st initialization of MariaDB. This user only has permissions on the MariaDB custom database", "form": true}, "password": {"type": "string", "title": "Password for MariaDB custom user", "description": "Defaults to a random 10-character alphanumeric string if not set", "form": true, "hidden": {"value": false, "path": "usePassword"}}, "replicationUser": {"type": "string", "title": "MariaDB replication user", "description": "Name of user used to manage replication.", "form": true, "hidden": {"value": "standalone", "path": "architecture"}}, "replicationPassword": {"type": "string", "title": "Password for MariaDB replication user", "description": "Defaults to a random 10-character alphanumeric string if not set", "form": true, "hidden": {"value": "standalone", "path": "architecture"}}}}, "primary": {"type": "object", "title": "Primary replicas settings", "form": true, "properties": {"persistence": {"type": "object", "title": "Persistence for primary replicas", "form": true, "properties": {"enabled": {"type": "boolean", "form": true, "title": "Enable persistence", "description": "Enable persistence using Persistent Volume Claims"}, "size": {"type": "string", "title": "Persistent Volume Size", "form": true, "render": "slider", "sliderMin": 1, "sliderMax": 100, "sliderUnit": "Gi", "hidden": {"value": false, "path": "persistence/enabled"}}}}}}, "secondary": {"type": "object", "title": "Secondary replicas settings", "form": true, "hidden": {"value": false, "path": "replication/enabled"}, "properties": {"persistence": {"type": "object", "title": "Persistence for secondary replicas", "form": true, "properties": {"enabled": {"type": "boolean", "form": true, "title": "Enable persistence", "description": "Enable persistence using Persistent Volume Claims"}, "size": {"type": "string", "title": "Persistent Volume Size", "form": true, "render": "slider", "sliderMin": 1, "sliderMax": 100, "sliderUnit": "Gi", "hidden": {"value": false, "path": "persistence/enabled"}}}}}}, "volumePermissions": {"type": "object", "properties": {"enabled": {"type": "boolean", "form": true, "title": "Enable Init Containers", "description": "Use an init container to set required folder permissions on the data volume before mounting it in the final destination"}}}, "metrics": {"type": "object", "form": true, "title": "Prometheus metrics details", "properties": {"enabled": {"type": "boolean", "title": "Create Prometheus metrics exporter", "description": "Create a side-car container to expose Prometheus metrics", "form": true}, "serviceMonitor": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "Create Prometheus Operator ServiceMonitor", "description": "Create a ServiceMonitor to track metrics using Prometheus Operator", "form": true, "hidden": {"value": false, "path": "metrics/enabled"}}}}}}}}