apiVersion: v1
kind: Service
metadata:
  name: {{ include "mariadb.primary.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: primary
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.primary.service.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.primary.service.annotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if and .Values.metrics.enabled .Values.metrics.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.annotations "context" $ ) | nindent 4 }}
    {{- end }}
spec:
  type: {{ .Values.primary.service.type }}
  {{- if and .Values.primary.service.clusterIP (eq .Values.primary.service.type "ClusterIP") }}
  clusterIP: {{ .Values.primary.service.clusterIP }}
  {{- end }}
  {{- if and .Values.primary.service.externalTrafficPolicy (or (eq .Values.primary.service.type "LoadBalancer") (eq .Values.primary.service.type "NodePort")) }}
  externalTrafficPolicy: {{ .Values.primary.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.primary.service.type "LoadBalancer") .Values.primary.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges: {{ .Values.primary.service.loadBalancerSourceRanges }}
  {{ end }}
  {{- if (and (eq .Values.primary.service.type "LoadBalancer") (not (empty .Values.primary.service.loadBalancerIP))) }}
  loadBalancerIP: {{ .Values.primary.service.loadBalancerIP }}
  {{- end }}
  {{- if .Values.primary.service.sessionAffinity }}
  sessionAffinity: {{ .Values.primary.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.primary.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.primary.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  ports:
    - name: mysql
      port: {{ coalesce .Values.primary.service.ports.mysql .Values.primary.service.port }}
      protocol: TCP
      targetPort: mysql
      {{- if (and (or (eq .Values.primary.service.type "NodePort") (eq .Values.primary.service.type "LoadBalancer")) (coalesce .Values.primary.service.nodePorts.mysql .Values.primary.service.nodePort)) }}
      nodePort: {{ coalesce .Values.primary.service.nodePorts.mysql .Values.primary.service.nodePort }}
      {{- else if eq .Values.primary.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if and .Values.metrics.enabled (gt (.Values.primary.service.ports.metrics | int) 0) }}
    - name: metrics
      port: {{ .Values.primary.service.ports.metrics }}
      protocol: TCP
      targetPort: metrics
    {{- end }}
    {{- if .Values.primary.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.primary.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  selector: {{ include "common.labels.matchLabels" . | nindent 4 }}
    app.kubernetes.io/component: primary
