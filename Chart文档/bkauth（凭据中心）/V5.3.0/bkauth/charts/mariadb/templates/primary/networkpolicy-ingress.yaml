{{- if and .Values.networkPolicy.enabled (or .Values.networkPolicy.metrics.enabled .Values.networkPolicy.ingressRules.primaryAccessOnlyFrom.enabled) }}
apiVersion: {{ include "common.capabilities.networkPolicy.apiVersion" . }}
kind: NetworkPolicy
metadata:
  name: {{ printf "%s-ingress" (include "common.names.fullname" .) }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/component: primary
      {{- include "common.labels.standard" . | nindent 6 }}
  ingress:
    {{- if and .Values.metrics.enabled .Values.networkPolicy.metrics.enabled (or .Values.networkPolicy.metrics.namespaceSelector .Values.networkPolicy.metrics.podSelector) }}
    - from:
        {{- if .Values.networkPolicy.metrics.namespaceSelector }}
        - namespaceSelector:
            matchLabels:
              {{- include "common.tplvalues.render" (dict "value" .Values.networkPolicy.metrics.namespaceSelector "context" $) | nindent 14 }}
        {{- end }}
        {{- if .Values.networkPolicy.metrics.podSelector }}
        - podSelector:
            matchLabels:
              {{- include "common.tplvalues.render" (dict "value" .Values.networkPolicy.metrics.podSelector "context" $) | nindent 14 }}
        {{- end }}
    {{- end }}
    {{- if and .Values.networkPolicy.ingressRules.primaryAccessOnlyFrom.enabled (or .Values.networkPolicy.ingressRules.primaryAccessOnlyFrom.namespaceSelector .Values.networkPolicy.ingressRules.primaryAccessOnlyFrom.podSelector) }}
    - from:
        {{- if .Values.networkPolicy.ingressRules.primaryAccessOnlyFrom.namespaceSelector }}
        - namespaceSelector:
            matchLabels:
              {{- include "common.tplvalues.render" (dict "value" .Values.networkPolicy.ingressRules.primaryAccessOnlyFrom.namespaceSelector "context" $) | nindent 14 }}
        {{- end }}
        {{- if .Values.networkPolicy.ingressRules.primaryAccessOnlyFrom.podSelector }}
        - podSelector:
            matchLabels:
              {{- include "common.tplvalues.render" (dict "value" .Values.networkPolicy.ingressRules.primaryAccessOnlyFrom.podSelector "context" $) | nindent 14 }}
        {{- end }}
    {{- end }}
    {{- if and .Values.networkPolicy.ingressRules.primaryAccessOnlyFrom.enabled (eq .Values.architecture "replication") }}
    - from:
        - podSelector:
            matchLabels:
              app.kubernetes.io/component: secondary
              {{- include "common.labels.standard" . | nindent 14 }}
    {{- end }}
    {{- if .Values.networkPolicy.ingressRules.primaryAccessOnlyFrom.customRules }}
    {{- include "common.tplvalues.render" (dict "value" .Values.networkPolicy.ingressRules.primaryAccessOnlyFrom.customRules "context" $) | nindent 4 }}
    {{- end }}
{{- end }}
