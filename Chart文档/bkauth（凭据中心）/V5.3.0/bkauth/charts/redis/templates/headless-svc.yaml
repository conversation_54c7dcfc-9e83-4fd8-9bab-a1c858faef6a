apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-headless" (include "common.names.fullname" .) }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
  {{- if .Values.commonAnnotations }}
  {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
  {{- include "redis.externalDNS.annotations" . | nindent 4 }}
spec:
  type: ClusterIP
  clusterIP: None
  {{- if .Values.sentinel.enabled }}
  publishNotReadyAddresses: true
  {{- end }}
  ports:
    - name: tcp-redis
      port: {{ if .Values.sentinel.enabled }}{{ .Values.sentinel.service.ports.redis }}{{ else }}{{ .Values.master.service.ports.redis }}{{ end }}
      targetPort: redis
    {{- if .Values.sentinel.enabled }}
    - name: tcp-sentinel
      port: {{ .Values.sentinel.service.ports.sentinel }}
      targetPort: redis-sentinel
    {{- end }}
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
