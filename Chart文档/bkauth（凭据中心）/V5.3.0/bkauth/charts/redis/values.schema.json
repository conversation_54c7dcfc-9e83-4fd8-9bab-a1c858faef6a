{"$schema": "http://json-schema.org/schema#", "type": "object", "properties": {"architecture": {"type": "string", "title": "Redis architecture", "form": true, "description": "Allowed values: `standalone` or `replication`", "enum": ["standalone", "replication"]}, "auth": {"type": "object", "title": "Authentication configuration", "form": true, "properties": {"enabled": {"type": "boolean", "form": true, "title": "Use password authentication"}, "password": {"type": "string", "title": "Redis password", "form": true, "description": "Defaults to a random 10-character alphanumeric string if not set", "hidden": {"value": false, "path": "auth/enabled"}}}}, "master": {"type": "object", "title": "Master replicas settings", "form": true, "properties": {"kind": {"type": "string", "title": "Workload Kind", "form": true, "description": "Allowed values: `Deployment` or `StatefulSet`", "enum": ["Deployment", "StatefulSet"]}, "persistence": {"type": "object", "title": "Persistence for master replicas", "form": true, "properties": {"enabled": {"type": "boolean", "form": true, "title": "Enable persistence", "description": "Enable persistence using Persistent Volume Claims"}, "size": {"type": "string", "title": "Persistent Volume Size", "form": true, "render": "slider", "sliderMin": 1, "sliderMax": 100, "sliderUnit": "Gi", "hidden": {"value": false, "path": "master/persistence/enabled"}}}}}}, "replica": {"type": "object", "title": "Redis replicas settings", "form": true, "hidden": {"value": "standalone", "path": "architecture"}, "properties": {"replicaCount": {"type": "integer", "form": true, "title": "Number of Redis replicas"}, "persistence": {"type": "object", "title": "Persistence for Redis replicas", "form": true, "properties": {"enabled": {"type": "boolean", "form": true, "title": "Enable persistence", "description": "Enable persistence using Persistent Volume Claims"}, "size": {"type": "string", "title": "Persistent Volume Size", "form": true, "render": "slider", "sliderMin": 1, "sliderMax": 100, "sliderUnit": "Gi", "hidden": {"value": false, "path": "replica/persistence/enabled"}}}}}}, "volumePermissions": {"type": "object", "properties": {"enabled": {"type": "boolean", "form": true, "title": "Enable Init Containers", "description": "Use an init container to set required folder permissions on the data volume before mounting it in the final destination"}}}, "metrics": {"type": "object", "form": true, "title": "Prometheus metrics details", "properties": {"enabled": {"type": "boolean", "title": "Create Prometheus metrics exporter", "description": "Create a side-car container to expose Prometheus metrics", "form": true}, "serviceMonitor": {"type": "object", "properties": {"enabled": {"type": "boolean", "title": "Create Prometheus Operator ServiceMonitor", "description": "Create a ServiceMonitor to track metrics using Prometheus Operator", "form": true, "hidden": {"value": false, "path": "metrics/enabled"}}}}}}}}