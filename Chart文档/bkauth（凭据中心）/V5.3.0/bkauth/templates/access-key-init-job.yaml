apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "common.names.fullname" . }}-access-key-init-{{ .Release.Revision }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.migration.annotations "context" $ ) | nindent 4 }}
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
spec:
  backoffLimit: 10
  template:
    metadata:
      labels: {{- include "common.labels.standard" . | nindent 8 }}
        {{- if .Values.podLabels }}
        {{- include "common.tplvalues.render" (dict "value" .Values.podLabels "context" $) | nindent 8 }}
        {{- end }}
      annotations:
        checksum/configmap: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
      {{- if .Values.podAnnotations }}
        {{- include "common.tplvalues.render" (dict "value" .Values.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
    spec:
      {{- include "bkauth.imagePullSecrets" . | nindent 6 }}
      serviceAccountName: {{ template "bkauth.serviceAccountName" . }}
      restartPolicy: OnFailure
      initContainers:
        - name: check-migrate-job
          image: {{ include "bkauth.migration.k8sWaitFor.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          resources: {{- toYaml .Values.migration.resources | nindent 12 }}
          args:
            - "job"
            - "{{ include "common.names.fullname" . }}-migrate-{{ .Release.Revision }}"
      containers:
        - name: {{ template "bkauth.fullname" . }}
          {{- if .Values.containerSecurityContext.enabled }}
          securityContext:
            runAsUser: {{ .Values.containerSecurityContext.runAsUser }}
          {{- end }}
          image: {{ template "bkauth.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          command:
            - /app/bkauth
            - fixture_init
            - -c
            - /app/config.yaml
          {{- if .Values.extraEnvVars }}
          env:
            {{- include "common.tplvalues.render" (dict "value" .Values.extraEnvVars "context" $) | nindent 12 }}
          {{- end }}
          envFrom:
            {{- if .Values.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- if .Values.resources }}
          resources: {{- toYaml .Values.resources | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: config-volume
              mountPath: /app/config.yaml
              readOnly: true
              subPath: config.yaml
            {{- if .Values.extraVolumeMounts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.extraVolumeMounts "context" $) | nindent 12 }}
            {{- end }}
        {{- if .Values.sidecars }}
        {{- include "common.tplvalues.render" (dict "value" .Values.sidecars "context" $) | nindent 8 }}
        {{- end }}
      volumes:
        - name: config-volume
          configMap:
            name: {{ template "bkauth.fullname" . }}-config
            items:
            - key: config.yaml
              path: config.yaml
        {{- if .Values.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.extraVolumes "context" $) | nindent 8 }}
        {{- end }}
