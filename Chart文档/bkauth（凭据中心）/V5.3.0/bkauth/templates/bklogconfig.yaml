{{- if .Values.bkLogConfig.enabled }}
# Stdout Log
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ template "bkauth.fullname" . }}-stdout-log
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
  {{- end }}
spec:
  dataId: {{ .Values.bkLogConfig.dataId }}
  logConfigType: std_log_config
  namespace: {{ .Release.Namespace | quote }}
  containerNameMatch:
    - {{ template "bkauth.fullname" . }}
  labelSelector:
    matchLabels:
      {{- include "common.labels.matchLabels" . | nindent 6 }}
  encoding: 'utf-8'
---
# Container Log
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ template "bkauth.fullname" . }}-container-log
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
  {{- end }}
spec:
  dataId: {{ .Values.bkLogConfig.dataId }}
  logConfigType: container_log_config
  namespace: {{ .Release.Namespace | quote }}
  containerNameMatch:
    - {{ template "bkauth.fullname" . }}
  labelSelector:
    matchLabels:
      {{- include "common.labels.matchLabels" . | nindent 6 }}
  path:
    - /app/logs/*.log
  encoding: 'utf-8'
{{- end }}
