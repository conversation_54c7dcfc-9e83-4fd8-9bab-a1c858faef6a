## @section Global parameters
## Global Docker image parameters
## Please, note that this will override the image parameters, including dependencies, configured to use the global value
## Current available global Docker image parameters: imageRegistry, imagePullSecrets and storageClass

## @param global.imageRegistry Global Docker image registry
## @param global.imagePullSecrets Global Docker registry secret names as an array
## @param global.storageClass Global StorageClass for Persistent Volume(s)
##
global:
  imageRegistry: ""
  ## E.g.
  ## imagePullSecrets:
  ##   - myRegistryKeySecretName
  ##
  imagePullSecrets: []
  storageClass: ""

## @section Common parameters

## @param kubeVersion Override Kubernetes version
##
kubeVersion: ""
## @param nameOverride String to partially override common.names.fullname
##
nameOverride: ""
## @param fullnameOverride String to fully override common.names.fullname
##
fullnameOverride: ""
## @param commonLabels Labels to add to all deployed objects
##
commonLabels: {}
## @param commonAnnotations Annotations to add to all deployed objects
##
commonAnnotations: {}
## @param extraDeploy Array of extra objects to deploy with the release
##
extraDeploy: []

## bkauth image
## @param image.registry bkauth image registry
## @param image.repository bkauth image repository
## @param image.tag bkauth image tag (immutable tags are recommended)
## @param image.pullPolicy bkauth image pull policy
## @param image.pullSecrets bkauth image pull secrets
## @param image.debug Enable image debug mode
##
image:
  registry: "hub.bktencent.com"
  repository: blueking/bkauth
  tag: v0.0.13
  ## Specify a imagePullPolicy
  ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
  ## ref: http://kubernetes.io/docs/user-guide/images/#pre-pulling-images
  ##
  pullPolicy: IfNotPresent
  ## Optionally specify an array of imagePullSecrets.
  ## Secrets must be manually created in the namespace.
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
  ## e.g:
  ## pullSecrets:
  ##   - myRegistryKeySecretName
  ##
  pullSecrets: []

## @param extraEnvVars Array with extra environment variables to add to the bkauth container
## e.g:
## extraEnvVars:
##   - name: FOO
##     value: "bar"
##
extraEnvVars: []

## @param extraEnvVarsCM Name of existing ConfigMap containing extra env vars
##
extraEnvVarsCM: ""
## @param extraEnvVarsSecret Name of existing Secret containing extra env vars
##
extraEnvVarsSecret: ""

## @section bkauth deployment parameters

## @param replicaCount Number of bkauth replicas to deploy
## NOTE: ReadWriteMany PVC(s) are required if replicaCount > 1
##
replicaCount: 1
## @param updateStrategy.type bkauth deployment strategy type
## @param updateStrategy.rollingUpdate bkauth deployment rolling update configuration parameters
## ref: https://kubernetes.io/docs/concepts/workloads/controllers/deployment/#strategy
## NOTE: Set it to `Recreate` if you use a PV that cannot be mounted on multiple pods
## e.g:
## updateStrategy:
##  type: RollingUpdate
##  rollingUpdate:
##    maxSurge: 25%
##    maxUnavailable: 25%
##
updateStrategy:
  type: RollingUpdate
  rollingUpdate: {}
## @param schedulerName Alternate scheduler
## ref: https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
##
schedulerName: ""
## @param serviceAccount.create Specifies whether a ServiceAccount should be created
## @param serviceAccount.name The name of the ServiceAccount to create
##
serviceAccount:
  create: true
  name: ""
## @param hostAliases [array] bkauth pod host aliases
## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
##
hostAliases: []
## @param extraVolumes Optionally specify extra list of additional volumes for bkauth pods
##
extraVolumes: []
## @param extraVolumeMounts Optionally specify extra list of additional volumeMounts for bkauth container(s)
##
extraVolumeMounts: []
## @param sidecars Add additional sidecar containers to the bkauth pod
## e.g:
## sidecars:
##   - name: your-image-name
##     image: your-image
##     imagePullPolicy: Always
##     ports:
##       - name: portname
##         containerPort: 1234
##
sidecars: []
## @param initContainers Add additional init containers to the bkauth pods
## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
## e.g:
## initContainers:
##  - name: your-image-name
##    image: your-image
##    imagePullPolicy: Always
##    command: ['sh', '-c', 'copy addons from git and push to /bkauth/addons. Should work with extraVolumeMounts and extraVolumes']
##
initContainers: []
## @param podLabels Extra labels for bkauth pods
## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
##
podLabels: {}
## @param podAnnotations Annotations for bkauth pods
## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
##
podAnnotations: {}
## @param podAffinityPreset Pod affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
##
podAffinityPreset: ""
## @param podAntiAffinityPreset Pod anti-affinity preset. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
##
podAntiAffinityPreset: soft
## Node affinity preset
## Ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
##
nodeAffinityPreset:
  ## @param nodeAffinityPreset.type Node affinity preset type. Ignored if `affinity` is set. Allowed values: `soft` or `hard`
  ##
  type: ""
  ## @param nodeAffinityPreset.key Node label key to match. Ignored if `affinity` is set
  ##
  key: ""
  ## @param nodeAffinityPreset.values Node label values to match. Ignored if `affinity` is set
  ## E.g.
  ## values:
  ##   - e2e-az1
  ##   - e2e-az2
  ##
  values: []
## @param affinity Affinity for pod assignment
## Ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
## NOTE: podAffinityPreset, podAntiAffinityPreset, and  nodeAffinityPreset will be ignored when it's set
##
affinity: {}
## @param nodeSelector Node labels for pod assignment
## ref: https://kubernetes.io/docs/user-guide/node-selection/
##
nodeSelector: {}
## @param tolerations Tolerations for pod assignment
## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
##
tolerations: []
## bkauth containers' resource requests and limits
## ref: http://kubernetes.io/docs/user-guide/compute-resources/
## @param resources.limits The resources limits for the bkauth container
## @param resources.requests [object] The requested resources for the bkauth container
##
resources:
  limits:
    memory: 4096Mi
    cpu: 2000m
  requests:
    memory: 512Mi
    cpu: 500m
## Container ports
## @param containerPort bkauth HTTP container port
##
containerPort: 5000
## Configure Pods Security Context
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
## @param podSecurityContext.enabled Enabled bkauth pods' Security Context
## @param podSecurityContext.fsGroup Set bkauth pod's Security Context fsGroup
##
podSecurityContext:
  enabled: false
  fsGroup: 1001
## Configure Container Security Context (only main container)
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
## @param containerSecurityContext.enabled Enabled bkauth containers' Security Context
## @param containerSecurityContext.runAsUser Set bkauth container's Security Context runAsUser
##
containerSecurityContext:
  enabled: false
  runAsUser: 1001
## Configure extra options for bkauth containers' liveness and readiness probes
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-probes/#configure-probes
## @param livenessProbe.enabled Enable livenessProbe
## @param livenessProbe.path Path for to check for livenessProbe
## @param livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
## @param livenessProbe.periodSeconds Period seconds for livenessProbe
## @param livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
## @param livenessProbe.failureThreshold Failure threshold for livenessProbe
## @param livenessProbe.successThreshold Success threshold for livenessProbe
##
livenessProbe:
  enabled: true
  path: /ping
  initialDelaySeconds: 5
  periodSeconds: 30
  timeoutSeconds: 5
  failureThreshold: 6
  successThreshold: 1
## @param readinessProbe.enabled Enable readinessProbe
## @param readinessProbe.path Path to check for readinessProbe
## @param readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
## @param readinessProbe.periodSeconds Period seconds for readinessProbe
## @param readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
## @param readinessProbe.failureThreshold Failure threshold for readinessProbe
## @param readinessProbe.successThreshold Success threshold for readinessProbe
##
readinessProbe:
  enabled: true
  path: /healthz
  initialDelaySeconds: 5
  periodSeconds: 10
  timeoutSeconds: 5
  failureThreshold: 6
  successThreshold: 1

## @param customLivenessProbe Custom livenessProbe that overrides the default one
##
customLivenessProbe: {}
## @param customReadinessProbe Custom readinessProbe that overrides the default one
#
customReadinessProbe: {}

## bkauth migration parameters
migration:
  images:
    busybox:
      registry: docker.io
      repository: library/busybox
      tag: 1.34.0
    k8sWaitFor:
      registry: docker.io
      repository: groundnuty/k8s-wait-for
      tag: v1.5.1

  resources:
    limits:
      cpu: 1000m
      memory: 1024Mi
    requests:
      cpu: 100m
      memory: 256Mi

  rbac:
    create: true

## bkauth service parameters
##
service:
  ## @param service.type bkauth service type
  ##
  type: ClusterIP
  ## @param service.port bkauth service HTTP port
  ##
  port: 80
  ## Node ports to expose
  ## @param service.nodePort Node port for HTTP
  ## NOTE: choose port between <30000-32767>
  ##
  nodePort: ""
  ## @param service.clusterIP bkauth service Cluster IP
  ## e.g.:
  ## clusterIP: None
  ##
  clusterIP: ""
  ## @param service.loadBalancerIP bkauth service Load Balancer IP
  ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
  ##
  loadBalancerIP: ""
  ## @param service.loadBalancerSourceRanges bkauth service Load Balancer sources
  ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
  ## e.g:
  ## loadBalancerSourceRanges:
  ##   - **********/24
  ##
  loadBalancerSourceRanges: []
  ## @param service.externalTrafficPolicy bkauth service external traffic policy
  ## ref http://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
  ##
  externalTrafficPolicy: Cluster
  ## @param service.annotations Additional custom annotations for bkauth service
  ##
  annotations: {}
  ## @param service.extraPorts Extra port to expose on bkauth service
  ##
  extraPorts: []

## Configure the ingress resource that allows you to access the bkauth installation
## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/
##
ingress:
  ## @param ingress.enabled Enable ingress record generation for bkauth
  ##
  enabled: true
  ## DEPRECATED: Use ingress.annotations instead of ingress.certManager
  ## certManager: false
  ##

  ## @param ingress.selfSigned Create a TLS secret for this ingress record using self-signed certificates generated by Helm
  ##
  selfSigned: false
  ## @param ingress.pathType Ingress path type
  ##
  pathType: ImplementationSpecific
  ## @param ingress.apiVersion Force Ingress API version (automatically detected if not set)
  ##
  apiVersion: ""
  ## @param ingress.hostname Default host for the ingress record
  ##
  hostname: bkauth.example.com
  ## @param ingress.path Default path for the ingress record
  ## NOTE: You may need to set this to '/*' in order to use this with ALB ingress controllers
  ##
  path: /
  ## @param ingress.annotations Additional annotations for the Ingress resource. To enable certificate autogeneration, place here your cert-manager annotations.
  ## For a full list of possible ingress annotations, please see
  ## ref: https://github.com/kubernetes/ingress-nginx/blob/master/docs/user-guide/nginx-configuration/annotations.md
  ## Use this parameter to set the required annotations for cert-manager, see
  ## ref: https://cert-manager.io/docs/usage/ingress/#supported-annotations
  ##
  ## e.g:
  ## annotations:
  ##   kubernetes.io/ingress.class: nginx
  ##   cert-manager.io/cluster-issuer: cluster-issuer-name
  ##
  annotations: {}
  ## @param ingress.tls Enable TLS configuration for the host defined at `ingress.hostname` parameter
  ## TLS certificates will be retrieved from a TLS secret with name: `{{- printf "%s-tls" .Values.ingress.hostname }}`
  ## You can:
  ##   - Use the `ingress.secrets` parameter to create this TLS secret
  ##   - Relay on cert-manager to create it by setting the corresponding annotations
  ##   - Relay on Helm to create self-signed certificates by setting `ingress.tls=true` and `ingress.certManager=false`
  ##
  tls: false
  ## @param ingress.extraHosts An array with additional hostname(s) to be covered with the ingress record
  ## e.g:
  ## extraHosts:
  ##   - name: bkauth.local
  ##     path: /
  ##
  extraHosts: []
  ## @param ingress.extraPaths An array with additional arbitrary paths that may need to be added to the ingress under the main host
  ## e.g:
  ## extraPaths:
  ## - path: /*
  ##   backend:
  ##     serviceName: ssl-redirect
  ##     servicePort: use-annotation
  ##
  extraPaths: []
  ## @param ingress.extraTls TLS configuration for additional hostname(s) to be covered with this ingress record
  ## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/#tls
  ## e.g:
  ## extraTls:
  ## - hosts:
  ##     - bkauth.local
  ##   secretName: bkauth.local-tls
  ##
  extraTls: []
  ## @param ingress.secrets Custom TLS certificates as secrets
  ## NOTE: 'key' and 'certificate' are expected in PEM format
  ## NOTE: 'name' should line up with a 'secretName' set further up
  ## If it is not set and you're using cert-manager, this is unneeded, as it will create a secret for you with valid certificates
  ## If it is not set and you're NOT using cert-manager either, self-signed certificates will be created valid for 365 days
  ## It is also possible to create and manage the certificates outside of this helm chart
  ## Please see README.md for more information
  ## e.g:
  ## secrets:
  ##   - name: bkauth.local-tls
  ##     key: |-
  ##       -----BEGIN RSA PRIVATE KEY-----
  ##       ...
  ##       -----END RSA PRIVATE KEY-----
  ##     certificate: |-
  ##       -----BEGIN CERTIFICATE-----
  ##       ...
  ##       -----END CERTIFICATE-----
  ##
  secrets: []

## @section Other Parameters

## bkauth Autoscaling configuration
## ref: https://kubernetes.io/docs/tasks/run-application/horizontal-pod-autoscale/
## @param autoscaling.enabled Enable Horizontal POD autoscaling for bkauth
## @param autoscaling.minReplicas Minimum number of bkauth replicas
## @param autoscaling.maxReplicas Maximum number of bkauth replicas
## @param autoscaling.targetCPU Target CPU utilization percentage
## @param autoscaling.targetMemory Target Memory utilization percentage
##
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 11
  targetCPU: 50
  targetMemory: 50

## @section bkauth config.yaml Parameters

## 用于生成bkauth的config.yaml的配置
## 是否开启调试日志
debug: false

## 启用sentry
sentryDsn: ""

## 配置AppSecret在DB的加密密钥，由大小写字母和数字组成的32位字符串
encryptKey: ""

## 配置需要初始化的AppCode和AppSecret，格式map[appCode] = appSecret
## accessKeys:
##   bk_test_app_code: "testSecretLength36"
accessKeys: {}

## 日志输出配置，默认os，标准输出，可选择 file
logWriter: "os"

## @section Database Parameters

## MariaDB chart configuration
## ref: https://github.com/bitnami/charts/blob/master/bitnami/mariadb/values.yaml
##
mariadb:
  ## @param mariadb.enabled Deploy a MariaDB server to satisfy the applications database requirements
  ## To use an external database set this to false and configure the `externalDatabase.*` parameters
  ##
  enabled: true
  ## @param mariadb.architecture MariaDB architecture. Allowed values: `standalone` or `replication`
  ##
  architecture: standalone
  ## MariaDB Authentication parameters
  ## @param mariadb.auth.rootPassword MariaDB root password
  ## @param mariadb.auth.database MariaDB custom database
  ## @param mariadb.auth.username MariaDB custom user name
  ## @param mariadb.auth.password MariaDB custom user password
  ## ref: https://github.com/bitnami/bitnami-docker-mariadb#setting-the-root-password-on-first-run
  ##      https://github.com/bitnami/bitnami-docker-mariadb/blob/master/README.md#creating-a-database-on-first-run
  ##      https://github.com/bitnami/bitnami-docker-mariadb/blob/master/README.md#creating-a-database-user-on-first-run
  auth:
    rootPassword: "blueking"
    database: bkauth
    username: bkauth
    password: "bkauth"
  ## MariaDB Primary configuration
  ##
  primary:
    ## MariaDB Primary Persistence parameters
    ## ref: http://kubernetes.io/docs/user-guide/persistent-volumes/
    ## @param mariadb.primary.persistence.enabled Enable persistence on MariaDB using PVC(s)
    ## @param mariadb.primary.persistence.storageClass Persistent Volume storage class
    ## @param mariadb.primary.persistence.accessModes [array] Persistent Volume access modes
    ## @param mariadb.primary.persistence.size Persistent Volume size
    ##
    persistence:
      enabled: true
      storageClass:
      accessModes:
        - ReadWriteOnce
      size: 1Gi
  ## mariadb init scripts
  ##
  ## initdbScriptsConfigMap: "bkauth-mariadb-init"

## External Database Configuration
## All of these values are only used if `mariadb.enabled=false`
##
externalDatabase:
  default:
    ## @param externalDatabase.default.host External Database server host
    ##
    host: localhost
    ## @param externalDatabase.default.port External Database server port
    ##
    port: 3306
    ## @param externalDatabase.default.user External Database username
    ##
    user: bkauth
    ## @param externalDatabase.default.password External Database user password
    ##
    password: ""
    ## @param externalDatabase.default.database External Database database name
    ##
    database: bkauth
    ## @param externalDatabase.existingSecret The name of an existing secret with database credentials
    ## NOTE: Must contain key `mariadb-password`
    ## NOTE: When it's set, the `externalDatabase.password` parameter is ignored
    ##
    existingSecret:

  openPaas:
    ## @param externalDatabase.openPaas.enabled 开启时, 优先使用外部db进行定时同步open_paas的AppSecret数据, false时默认不进行同步
    enabled: false

    ## @param externalDatabase.openPaas.host External Database server host
    ##
    host: bk-panel-mariadb
    ## @param externalDatabase.openPaas.port External Database server port
    ##
    port: 3306
    ## @param externalDatabase.openPaas.user External Database username
    ##
    user: bk_panel
    ## @param externalDatabase.openPaas.password External Database user password
    ##
    password: "root"
    ## @param externalDatabase.openPaas.database External Database database name
    ##
    database: open_paas
    ## @param externalDatabase.existingSecret The name of an existing secret with database credentials
    ## NOTE: Must contain key `mariadb-password`
    ## NOTE: When it's set, the `externalDatabase.password` parameter is ignored
    ##
    existingSecret:

## Redis(TM) chart configuration
## ref: https://github.com/bitnami/charts/blob/master/bitnami/redis/values.yaml
##
redis:
  enabled: true
  ## image:
  ##   tag:
 
  ## Use password authentication
  ##
  auth:
    enabled: true
    ## Redis(TM) password (both master and slave). Defaults to a random 10-character alphanumeric string if not set and auth.enabled is true.
    ## It should always be set using the password value or in the existingSecret to avoid issues
    ## with Sampo.
    ## The password value is ignored if existingSecret is set
    password: "bkauth"
    existingSecret:
 
  ##
  ## Cluster settings
  ##
  architecture: standalone

  ##
  ## Redis(TM) Master parameters
  ##
  master:
    persistence:
      enabled: true
      size: 1Gi
  replica:
    persistence:
      enabled: false

## If the Redis(TM) included in the chart is disabled, Harbor will use below
## Redis(TM) parameters to connect to an external Redis(TM) server.
## Support for both Redis(TM) and Redis+Sentinel(TM)
##
externalRedis:
  default:
    ## Redis(TM) host
    ##
    host: localhost
    ## Redis(TM) port number
    ##
    port: 6379
    ## Redis(TM) password
    ##
    password: ""
    ## Redis(TM) db name
    ##
    db: 0

    ## Redis(TM) sentinel configuration
    ## If sentinel is enabled the below sentinel configurations are used as the hostname
    ##
    sentinel:
      enabled: false
      masterSet: "mymaster"
      ## Configure redis sentinel hostnames in the following pattern
      ## <host_sentinal1>:<port_sentinel1>,<host_sentinal2>:<port_sentinel2>,<host_sentinal2>:<port_sentinel3>
      ##
      hosts: ""
      ## Redis(TM) sentinel password
      ##
      password: ""

## 蓝鲸日志采集
##
bkLogConfig:
  enabled: false
  dataId: 1

## ServiceMonitor configuration
##
serviceMonitor:
  ## @param serviceMonitor.enabled Creates a ServiceMonitor to monitor kube-state-metrics
  ##
  enabled: false
  ## @param serviceMonitor.namespace Namespace in which Prometheus is running
  ## e.g:
  ## namespace: monitoring
  ##
  namespace: ""
  ## @param serviceMonitor.jobLabel The name of the label on the target service to use as the job name in prometheus.
  ##
  jobLabel: ""
  ## @param serviceMonitor.interval Scrape interval (use by default, falling back to Prometheus' default)
  ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint
  ## e.g:
  ## interval: 10s
  ##
  interval: "30s"
  ## @param serviceMonitor.scrapeTimeout Timeout after which the scrape is ended
  ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint
  ## e.g:
  ## scrapeTimeout: 10s
  ##
  scrapeTimeout: ""
  ## @param serviceMonitor.selector ServiceMonitor selector labels
  ## ref: https://github.com/bitnami/charts/tree/master/bitnami/prometheus-operator#prometheus-configuration
  ## e.g:
  ## selector:
  ##   prometheus: my-prometheus
  ##
  selector: {}
  ## @param serviceMonitor.honorLabels Honor metrics labels
  ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint
  ## e.g:
  ## honorLabels: false
  ##
  honorLabels: false
  ## @param serviceMonitor.relabelings ServiceMonitor relabelings
  ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#relabelconfig
  ##
  relabelings: []
  ## @param serviceMonitor.metricRelabelings ServiceMonitor metricRelabelings
  ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#relabelconfig
  ##
  metricRelabelings: []
