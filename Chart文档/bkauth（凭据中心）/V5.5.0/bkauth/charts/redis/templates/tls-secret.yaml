{{- if (include "redis.createTlsSecret" .) }}
{{- $secretName := printf "%s-crt" (include "common.names.fullname" .) }}
{{- $existingCerts := (lookup "v1" "Secret" .Release.Namespace $secretName).data | default dict }}
{{- $ca := genCA "redis-ca" 365 }}
{{- $releaseNamespace := .Release.Namespace }}
{{- $clusterDomain := .Values.clusterDomain }}
{{- $fullname := include "common.names.fullname" . }}
{{- $serviceName := include "common.names.fullname" . }}
{{- $headlessServiceName := printf "%s-headless" (include "common.names.fullname" .) }}
{{- $altNames := list (printf "*.%s.%s.svc.%s" $serviceName $releaseNamespace $clusterDomain) (printf "%s.%s.svc.%s" $serviceName $releaseNamespace $clusterDomain) (printf "*.%s.%s.svc.%s" $headlessServiceName $releaseNamespace $clusterDomain) (printf "%s.%s.svc.%s" $headlessServiceName $releaseNamespace $clusterDomain) "127.0.0.1" "localhost" $fullname }}
{{- $crt := genSignedCert $fullname nil $altNames 365 $ca }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $secretName }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  ca.crt: {{ (get $existingCerts "ca.crt") | default ($ca.Cert | b64enc | quote ) }}
  tls.crt: {{ (get $existingCerts "tls.crt") | default ($crt.Cert | b64enc | quote) }}
  tls.key: {{ (get $existingCerts "tls.key") | default ($crt.Key | b64enc | quote) }}
{{- end }}
