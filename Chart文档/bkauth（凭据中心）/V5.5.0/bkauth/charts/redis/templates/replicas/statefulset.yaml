{{- if and (eq .Values.architecture "replication") (not .Values.sentinel.enabled) }}
apiVersion: {{ include "common.capabilities.statefulset.apiVersion" . }}
kind: StatefulSet
metadata:
  name: {{ printf "%s-replicas" (include "common.names.fullname" .) }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: replica
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  {{- if not .Values.replica.autoscaling.enabled }}
  replicas: {{ .Values.replica.replicaCount }}
  {{- end }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" . | nindent 6 }}
      app.kubernetes.io/component: replica
  serviceName: {{ printf "%s-headless" (include "common.names.fullname" .) }}
  {{- if .Values.replica.updateStrategy }}
  updateStrategy: {{- toYaml .Values.replica.updateStrategy | nindent 4 }}
  {{- end }}
  {{- if .Values.replica.podManagementPolicy }}
  podManagementPolicy: {{ .Values.replica.podManagementPolicy | quote }}
  {{- end }}
  template:
    metadata:
      labels: {{- include "common.labels.standard" . | nindent 8 }}
        app.kubernetes.io/component: replica
        {{- if .Values.replica.podLabels }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.replica.podLabels "context" $ ) | nindent 8 }}
        {{- end }}
        {{- if and .Values.metrics.enabled .Values.metrics.podLabels }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.podLabels "context" $ ) | nindent 8 }}
        {{- end }}
      annotations:
        {{- if (include "redis.createConfigmap" .) }}
        checksum/configmap: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        {{- end }}
        checksum/health: {{ include (print $.Template.BasePath "/health-configmap.yaml") . | sha256sum }}
        checksum/scripts: {{ include (print $.Template.BasePath "/scripts-configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/secret.yaml") . | sha256sum }}
        {{- if .Values.replica.podAnnotations }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.replica.podAnnotations "context" $ ) | nindent 8 }}
        {{- end }}
        {{- if and .Values.metrics.enabled .Values.metrics.podAnnotations }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.podAnnotations "context" $ ) | nindent 8 }}
        {{- end }}
    spec:
      {{- include "redis.imagePullSecrets" . | nindent 6 }}
      {{- if .Values.replica.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.replica.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.replica.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.replica.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ template "redis.serviceAccountName" . }}
      {{- if .Values.replica.priorityClassName }}
      priorityClassName: {{ .Values.replica.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.replica.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.replica.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.replica.podAffinityPreset "component" "replica" "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.replica.podAntiAffinityPreset "component" "replica" "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.replica.nodeAffinityPreset.type "key" .Values.replica.nodeAffinityPreset.key "values" .Values.replica.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.replica.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.replica.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.replica.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.replica.tolerations "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.replica.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.replica.topologySpreadConstraints "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.replica.shareProcessNamespace }}
      shareProcessNamespace: {{ .Values.replica.shareProcessNamespace }}
      {{- end }}
      {{- if .Values.replica.schedulerName }}
      schedulerName: {{ .Values.replica.schedulerName | quote }}
      {{- end }}
      {{- if .Values.replica.dnsPolicy }}
      dnsPolicy: {{ .Values.replica.dnsPolicy }}
      {{- end }}
      {{- if .Values.replica.dnsConfig }}
      dnsConfig: {{- include "common.tplvalues.render" (dict "value" .Values.replica.dnsConfig "context" $) | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: {{ .Values.replica.terminationGracePeriodSeconds }}
      containers:
        - name: redis
          image: {{ template "redis.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.replica.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.replica.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
          {{- end }}
          {{- if .Values.replica.containerSecurityContext.enabled }}
          securityContext: {{- omit .Values.replica.containerSecurityContext "enabled" | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.replica.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.replica.command "context" $) | nindent 12 }}
          {{- else }}
          command:
            - /bin/bash
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.replica.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.replica.args "context" $) | nindent 12 }}
          {{- else }}
          args:
            - -c
            - /opt/bitnami/scripts/start-scripts/start-replica.sh
          {{- end }}
          env:
            - name: BITNAMI_DEBUG
              value: {{ ternary "true" "false" (or .Values.image.debug .Values.diagnosticMode.enabled) | quote }}
            - name: REDIS_REPLICATION_MODE
              value: slave
            - name: REDIS_MASTER_HOST
            {{- if and (eq (int64 .Values.master.count) 1) (ne .Values.master.kind "Deployment") }}
              value: {{ template "common.names.fullname" . }}-master-0.{{ template "common.names.fullname" . }}-headless.{{ .Release.Namespace }}.svc.{{ .Values.clusterDomain }}
            {{- else }}
              value: {{ template "common.names.fullname" . }}-master.{{ .Release.Namespace }}.svc.{{ .Values.clusterDomain }}
            {{- end }}
            - name: REDIS_MASTER_PORT_NUMBER
              value: {{ .Values.master.containerPorts.redis | quote }}
            - name: ALLOW_EMPTY_PASSWORD
              value: {{ ternary "no" "yes" .Values.auth.enabled | quote }}
            {{- if .Values.auth.enabled }}
            {{- if .Values.auth.usePasswordFiles }}
            - name: REDIS_PASSWORD_FILE
              value: "/opt/bitnami/redis/secrets/redis-password"
            - name: REDIS_MASTER_PASSWORD_FILE
              value: "/opt/bitnami/redis/secrets/redis-password"
            {{- else }}
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "redis.secretName" . }}
                  key: {{ template "redis.secretPasswordKey" . }}
            - name: REDIS_MASTER_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "redis.secretName" . }}
                  key: {{ template "redis.secretPasswordKey" . }}
            {{- end }}
            {{- end }}
            - name: REDIS_TLS_ENABLED
              value: {{ ternary "yes" "no" .Values.tls.enabled | quote }}
            {{- if .Values.tls.enabled }}
            - name: REDIS_TLS_PORT
              value: {{ .Values.replica.containerPorts.redis | quote }}
            - name:  REDIS_TLS_AUTH_CLIENTS
              value: {{ ternary "yes" "no" .Values.tls.authClients | quote }}
            - name:  REDIS_TLS_CERT_FILE
              value: {{ template "redis.tlsCert" . }}
            - name:  REDIS_TLS_KEY_FILE
              value: {{ template "redis.tlsCertKey" . }}
            - name:  REDIS_TLS_CA_FILE
              value: {{ template "redis.tlsCACert" . }}
            {{- if .Values.tls.dhParamsFilename }}
            - name:  REDIS_TLS_DH_PARAMS_FILE
              value: {{ template "redis.tlsDHParams" . }}
            {{- end }}
            {{- else }}
            - name: REDIS_PORT
              value: {{ .Values.replica.containerPorts.redis | quote }}
            {{- end }}
            {{- if .Values.replica.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.replica.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          {{- if or .Values.replica.extraEnvVarsCM .Values.replica.extraEnvVarsSecret }}
          envFrom:
            {{- if .Values.replica.extraEnvVarsCM }}
            - configMapRef:
                name: {{ .Values.replica.extraEnvVarsCM }}
            {{- end }}
            {{- if .Values.replica.extraEnvVarsSecret }}
            - secretRef:
                name: {{ .Values.replica.extraEnvVarsSecret }}
            {{- end }}
          {{- end }}
          ports:
            - name: redis
              containerPort: {{ .Values.replica.containerPorts.redis }}
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.replica.startupProbe.enabled }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" (omit .Values.replica.startupProbe "enabled") "context" $) | nindent 12 }}
            tcpSocket:
              port: redis
          {{- else if .Values.replica.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.replica.customStartupProbe "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.replica.livenessProbe.enabled }}
          livenessProbe:
            initialDelaySeconds: {{ .Values.replica.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.replica.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ add1 .Values.replica.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.replica.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.replica.livenessProbe.failureThreshold }}
            exec:
              command:
                - sh
                - -c
                - /health/ping_liveness_local_and_master.sh {{ .Values.replica.livenessProbe.timeoutSeconds }}
          {{- else if .Values.replica.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.replica.customLivenessProbe "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.replica.readinessProbe.enabled }}
          readinessProbe:
            initialDelaySeconds: {{ .Values.replica.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.replica.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ add1 .Values.replica.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.replica.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.replica.readinessProbe.failureThreshold }}
            exec:
              command:
                - sh
                - -c
                - /health/ping_readiness_local_and_master.sh {{ .Values.replica.readinessProbe.timeoutSeconds }}
          {{- else if .Values.replica.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.replica.customReadinessProbe "context" $) | nindent 12 }}
          {{- end }}
          {{- end }}
          {{- if .Values.replica.resources }}
          resources: {{- toYaml .Values.replica.resources | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: start-scripts
              mountPath: /opt/bitnami/scripts/start-scripts
            - name: health
              mountPath: /health
            {{- if .Values.auth.usePasswordFiles }}
            - name: redis-password
              mountPath: /opt/bitnami/redis/secrets/
            {{- end }}
            - name: redis-data
              mountPath: /data
              subPath: {{ .Values.replica.persistence.subPath }}
            - name: config
              mountPath: /opt/bitnami/redis/mounted-etc
            - name: redis-tmp-conf
              mountPath: /opt/bitnami/redis/etc
            {{- if .Values.tls.enabled }}
            - name: redis-certificates
              mountPath: /opt/bitnami/redis/certs
              readOnly: true
            {{- end }}
            {{- if .Values.replica.extraVolumeMounts }}
            {{- include "common.tplvalues.render" ( dict "value" .Values.replica.extraVolumeMounts "context" $ ) | nindent 12 }}
            {{- end }}
        {{- if .Values.metrics.enabled }}
        - name: metrics
          image: {{ include "redis.metrics.image" . }}
          imagePullPolicy: {{ .Values.metrics.image.pullPolicy | quote }}
          {{- if .Values.metrics.containerSecurityContext.enabled }}
          securityContext: {{- omit .Values.metrics.containerSecurityContext "enabled" | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.metrics.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.metrics.command "context" $) | nindent 12 }}
          {{- else }}
          command:
            - /bin/bash
            - -c
            - |
              if [[ -f '/secrets/redis-password' ]]; then
              export REDIS_PASSWORD=$(cat /secrets/redis-password)
              fi
              redis_exporter{{- range $key, $value := .Values.metrics.extraArgs }} --{{ $key }}={{ $value }}{{- end }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            - name: REDIS_ALIAS
              value: {{ template "common.names.fullname" . }}
            {{- if .Values.auth.enabled }}
            - name: REDIS_USER
              value: default
            {{- if (not .Values.auth.usePasswordFiles) }}
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "redis.secretName" . }}
                  key: {{ template "redis.secretPasswordKey" . }}
            {{- end }}
            {{- end }}
            {{- if .Values.tls.enabled }}
            - name: REDIS_ADDR
              value: rediss://{{ .Values.metrics.redisTargetHost }}:{{ .Values.replica.containerPorts.redis }}
              {{- if .Values.tls.authClients }}
            - name: REDIS_EXPORTER_TLS_CLIENT_KEY_FILE
              value: {{ template "redis.tlsCertKey" . }}
            - name: REDIS_EXPORTER_TLS_CLIENT_CERT_FILE
              value: {{ template "redis.tlsCert" . }}
              {{- end }}
            - name: REDIS_EXPORTER_TLS_CA_CERT_FILE
              value: {{ template "redis.tlsCACert" . }}
            {{- end }}
          ports:
            - name: metrics
              containerPort: 9121
          {{- if .Values.metrics.resources }}
          resources: {{- toYaml .Values.metrics.resources | nindent 12 }}
          {{- end }}
          volumeMounts:
            {{- if .Values.auth.usePasswordFiles }}
            - name: redis-password
              mountPath: /secrets/
            {{- end }}
            {{- if .Values.tls.enabled }}
            - name: redis-certificates
              mountPath: /opt/bitnami/redis/certs
              readOnly: true
            {{- end }}
            {{- if .Values.metrics.extraVolumeMounts }}
            {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.extraVolumeMounts "context" $ ) | nindent 12 }}
            {{- end }}
        {{- end }}
        {{- if .Values.replica.sidecars }}
        {{- include "common.tplvalues.render" (dict "value" .Values.replica.sidecars "context" $) | nindent 8 }}
        {{- end }}
      {{- $needsVolumePermissions := and .Values.volumePermissions.enabled .Values.replica.persistence.enabled .Values.replica.podSecurityContext.enabled .Values.replica.containerSecurityContext.enabled }}
      {{- if or .Values.replica.initContainers $needsVolumePermissions .Values.sysctl.enabled }}
      initContainers:
        {{- if .Values.replica.initContainers }}
        {{- include "common.tplvalues.render" (dict "value" .Values.replica.initContainers "context" $) | nindent 8 }}
        {{- end }}
        {{- if $needsVolumePermissions }}
        - name: volume-permissions
          image: {{ include "redis.volumePermissions.image" . }}
          imagePullPolicy: {{ .Values.volumePermissions.image.pullPolicy | quote }}
          command:
            - /bin/bash
            - -ec
            - |
              {{- if eq ( toString ( .Values.volumePermissions.containerSecurityContext.runAsUser )) "auto" }}
              chown -R `id -u`:`id -G | cut -d " " -f2` {{ .Values.replica.persistence.path }}
              {{- else }}
              chown -R {{ .Values.replica.containerSecurityContext.runAsUser }}:{{ .Values.replica.podSecurityContext.fsGroup }} {{ .Values.replica.persistence.path }}
              {{- end }}
          {{- if eq ( toString ( .Values.volumePermissions.containerSecurityContext.runAsUser )) "auto" }}
          securityContext: {{- omit .Values.volumePermissions.containerSecurityContext "runAsUser" | toYaml | nindent 12 }}
          {{- else }}
          securityContext: {{- .Values.volumePermissions.containerSecurityContext | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.volumePermissions.resources }}
          resources: {{- toYaml .Values.volumePermissions.resources | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: redis-data
              mountPath: {{ .Values.replica.persistence.path }}
              subPath: {{ .Values.replica.persistence.subPath }}
        {{- end }}
        {{- if .Values.sysctl.enabled }}
        - name: init-sysctl
          image: {{ include "redis.sysctl.image" . }}
          imagePullPolicy: {{ default "" .Values.sysctl.image.pullPolicy | quote }}
          securityContext:
            privileged: true
            runAsUser: 0
          {{- if .Values.sysctl.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.sysctl.command "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.sysctl.resources }}
          resources: {{- toYaml .Values.sysctl.resources | nindent 12 }}
          {{- end }}
          {{- if .Values.sysctl.mountHostSys }}
          volumeMounts:
            - name: host-sys
              mountPath: /host-sys
          {{- end }}
        {{- end }}
      {{- end }}
      volumes:
        - name: start-scripts
          configMap:
            name: {{ printf "%s-scripts" (include "common.names.fullname" .) }}
            defaultMode: 0755
        - name: health
          configMap:
            name: {{ printf "%s-health" (include "common.names.fullname" .) }}
            defaultMode: 0755
        {{- if .Values.auth.usePasswordFiles }}
        - name: redis-password
          secret:
            secretName: {{ template "redis.secretName" . }}
            items:
            - key: {{ template "redis.secretPasswordKey" . }}
              path: redis-password
        {{- end }}
        - name: config
          configMap:
            name: {{ include "redis.configmapName" . }}
        {{- if .Values.sysctl.mountHostSys }}
        - name: host-sys
          hostPath:
            path: /sys
        {{- end }}
        - name: redis-tmp-conf
          {{- if .Values.replica.persistence.medium }}
          emptyDir:
            medium: {{ .Values.replica.persistence.medium | quote }}
            {{- if .Values.replica.persistence.sizeLimit }}
            sizeLimit: {{ .Values.replica.persistence.sizeLimit | quote }}
            {{- end }}
          {{- else }}
          emptyDir: {}
          {{- end }}
        {{- if .Values.tls.enabled }}
        - name: redis-certificates
          secret:
            secretName: {{ include "redis.tlsSecretName" . }}
            defaultMode: 256
        {{- end }}
        {{- if .Values.replica.extraVolumes }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.replica.extraVolumes "context" $ ) | nindent 8 }}
        {{- end }}
        {{- if .Values.metrics.extraVolumes }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.extraVolumes "context" $ ) | nindent 8 }}
        {{- end }}
  {{- if not .Values.replica.persistence.enabled }}
        - name: redis-data
          {{- if .Values.replica.persistence.medium }}
          emptyDir: {
            medium: {{ .Values.replica.persistence.medium | quote }}
          }
          {{- else }}
          emptyDir: {}
          {{- end }}
  {{- else if .Values.replica.persistence.existingClaim }}
        - name: redis-data
          persistentVolumeClaim:
            claimName: {{ printf "%s" (tpl .Values.replica.persistence.existingClaim .) }}
  {{- else }}
  volumeClaimTemplates:
    - metadata:
        name: redis-data
        labels: {{- include "common.labels.matchLabels" . | nindent 10 }}
          app.kubernetes.io/component: replica
        {{- if .Values.replica.persistence.annotations }}
        annotations: {{- toYaml .Values.replica.persistence.annotations | nindent 10 }}
        {{- end }}
      spec:
        accessModes:
        {{- range .Values.replica.persistence.accessModes }}
          - {{ . | quote }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.replica.persistence.size | quote }}
        {{- if .Values.replica.persistence.selector }}
        selector: {{- include "common.tplvalues.render" (dict "value" .Values.replica.persistence.selector "context" $) | nindent 10 }}
        {{- end }}
        {{- if .Values.replica.persistence.dataSource }}
        dataSource: {{- include "common.tplvalues.render" (dict "value" .Values.replica.persistence.dataSource "context" $) | nindent 10 }}
        {{- end }}
        {{- include "common.storage.class" (dict "persistence" .Values.replica.persistence "global" .Values.global) | nindent 8 }}
  {{- end }}
{{- end }}
