{{- if .Values.metrics.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-metrics" (include "common.names.fullname" .) }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: metrics
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if or .Values.metrics.service.annotations .Values.commonAnnotations }}
  annotations:
    {{- if .Values.metrics.service.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.service.annotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
  {{- end }}
spec:
  type: {{ .Values.metrics.service.type }}
  {{- if eq .Values.metrics.service.type "LoadBalancer" }}
  externalTrafficPolicy: {{ .Values.metrics.service.externalTrafficPolicy }}
  {{- end }}
  {{- if and (eq .Values.metrics.service.type "LoadBalancer") .Values.metrics.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.metrics.service.loadBalancerIP }}
  {{- end }}
  {{- if and (eq .Values.metrics.service.type "LoadBalancer") .Values.metrics.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges: {{- toYaml .Values.metrics.service.loadBalancerSourceRanges | nindent 4 }}
  {{- end }}
  ports:
    - name: http-metrics
      port: {{ .Values.metrics.service.port }}
      protocol: TCP
      targetPort: metrics
    {{- if .Values.metrics.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.metrics.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
{{- end }}
