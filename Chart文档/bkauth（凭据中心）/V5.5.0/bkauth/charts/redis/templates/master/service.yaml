{{- if not .Values.sentinel.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-master" (include "common.names.fullname" .) }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: master
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if or .Values.master.service.annotations .Values.commonAnnotations }}
  annotations:
    {{- if .Values.master.service.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.master.service.annotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
  {{- end }}
spec:
  type: {{ .Values.master.service.type }}
  {{- if or (eq .Values.master.service.type "LoadBalancer") (eq .Values.master.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.master.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if (semverCompare ">=1.22-0" (include "common.capabilities.kubeVersion" .)) }}
  internalTrafficPolicy: {{ .Values.master.service.internalTrafficPolicy }}
  {{- end }}
  {{- if and (eq .Values.master.service.type "LoadBalancer") (not (empty .Values.master.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.master.service.loadBalancerIP }}
  {{- end }}
  {{- if and (eq .Values.master.service.type "LoadBalancer") (not (empty .Values.master.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.master.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and .Values.master.service.clusterIP (eq .Values.master.service.type "ClusterIP") }}
  clusterIP: {{ .Values.master.service.clusterIP }}
  {{- end }}
  {{- if .Values.master.service.sessionAffinity }}
  sessionAffinity: {{ .Values.master.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.master.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.master.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  ports:
    - name: tcp-redis
      port: {{ .Values.master.service.ports.redis }}
      targetPort: redis
      {{- if and (or (eq .Values.master.service.type "NodePort") (eq .Values.master.service.type "LoadBalancer")) .Values.master.service.nodePorts.redis}}
      nodePort: {{ .Values.master.service.nodePorts.redis}}
      {{- else if eq .Values.master.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if .Values.master.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.master.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
    app.kubernetes.io/component: master
{{- end }}
