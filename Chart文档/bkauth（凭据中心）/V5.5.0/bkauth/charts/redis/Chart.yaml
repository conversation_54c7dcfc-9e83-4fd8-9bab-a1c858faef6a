annotations:
  category: Database
apiVersion: v2
appVersion: 6.2.7
dependencies:
- name: common
  repository: https://charts.bitnami.com/bitnami
  tags:
  - bitnami-common
  version: 1.x.x
description: Redis(R) is an open source, advanced key-value store. It is often referred
  to as a data structure server since keys can contain strings, hashes, lists, sets
  and sorted sets.
home: https://github.com/bitnami/charts/tree/master/bitnami/redis
icon: https://bitnami.com/assets/stacks/redis/img/redis-stack-220x234.png
keywords:
- redis
- keyvalue
- database
maintainers:
- name: Bitnami
  url: https://github.com/bitnami/charts
- email: <EMAIL>
  name: desaintmartin
name: redis
sources:
- https://github.com/bitnami/bitnami-docker-redis
version: 16.13.2
