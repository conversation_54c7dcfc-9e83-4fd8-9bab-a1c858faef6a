{{- if (include "mariadb.secondary.createConfigmap" .) }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "mariadb.secondary.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: secondary
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
data:
  my.cnf: |-
{{ .Values.secondary.configuration | indent 4 }}
{{- end -}}
