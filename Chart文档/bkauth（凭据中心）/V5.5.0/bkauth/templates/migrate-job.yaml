apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "common.names.fullname" . }}-migrate-{{ .Release.Revision }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: migration
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.migration.annotations "context" $ ) | nindent 4 }}
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
spec:
  backoffLimit: 10
  template:
    metadata:
      labels: {{- include "common.labels.standard" . | nindent 8 }}
        app.kubernetes.io/component: migration
        {{- if .Values.podLabels }}
        {{- include "common.tplvalues.render" (dict "value" .Values.podLabels "context" $) | nindent 8 }}
        {{- end }}
      {{- if .Values.podAnnotations }}
       annotations:
        {{- include "common.tplvalues.render" (dict "value" .Values.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
    spec:
      {{- include "bkauth.imagePullSecrets" . | nindent 6 }}
      restartPolicy: OnFailure
      initContainers:
        - name: check-database
          image: {{ include "bkauth.migration.busybox.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          resources: {{- toYaml .Values.migration.resources | nindent 12 }}
          command:
            - sh
            - -c
            - "until telnet {{ template "bkauth.databaseHost" . }} {{ template "bkauth.databasePort" . }}; do echo waiting for db {{ template "bkauth.databaseHost" . }}; sleep 2; done;"
      containers:
        - name: {{ template "bkauth.fullname" . }}
          {{- if .Values.containerSecurityContext.enabled }}
          securityContext:
            runAsUser: {{ .Values.containerSecurityContext.runAsUser }}
          {{- end }}
          image: {{ template "bkauth.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          command: ["/bin/bash"]
          {{- if and .Values.encrypt .Values.encrypt.args .Values.encrypt.args.migrateJob }}
          args: ["-c", {{ .Values.encrypt.args.migrateJob | quote }}]
          {{- else }}
          args: ["-c", "/app/sql-migrate up -config=/conf/dbconfig.yml -env=production"]
          {{- end }}
          env:
            - name: MYSQL_HOST
              value: {{ template "bkauth.databaseHost" . }}
            - name: MYSQL_PORT
              value: "{{ template "bkauth.databasePort" . }}"
            - name: MYSQL_NAME
              value: {{ template "bkauth.databaseName" . }}
            - name: MYSQL_USER
              value: {{ template "bkauth.databaseUser" . }}
            - name: MYSQL_PASSWORD
              value: {{ template "bkauth.databasePassword" . }}
            {{- if .Values.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.extraEnvVarsSecret "context" $) }}
            {{- end }}
          {{- if .Values.resources }}
          resources: {{- toYaml .Values.resources | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: sql-migrate-dbconfig
              mountPath: /conf
            {{- if .Values.extraVolumeMounts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.extraVolumeMounts "context" $) | nindent 12 }}
            {{- end }}
        {{- if .Values.sidecars }}
        {{- include "common.tplvalues.render" (dict "value" .Values.sidecars "context" $) | nindent 8 }}
        {{- end }}
      volumes:
        - name: sql-migrate-dbconfig
          configMap:
            name: {{ template "bkauth.fullname" . }}-dbconfig
        {{- if .Values.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.extraVolumes "context" $) | nindent 8 }}
        {{- end }}
