*******************************************************************
*** PLEASE BE PATIENT: b<PERSON><PERSON> may take a few minutes to install   ***
*******************************************************************

{{- if .Values.ingress.enabled }}

1. Get the bkauth URL indicated on the Ingress Rule and associate it to your cluster external IP:

   export CLUSTER_IP=$(minikube ip) # On Minikube. Use: `kubectl cluster-info` on others K8s clusters
   export HOSTNAME=$(kubectl get ingress --namespace {{ .Release.Namespace }} {{ template "bkauth.fullname" . }} -o jsonpath='{.spec.rules[0].host}')
   echo "bkauth URL: http://$HOSTNAME/"
   echo "$CLUSTER_IP  $HOSTNAME" | sudo tee -a /etc/hosts

{{- else }}

1. Get the bkauth URL by running:

{{- if eq .Values.service.type "NodePort" }}

  export NODE_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services {{ template "bkauth.fullname" . }})
  export NODE_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
  echo "bkauth URL: http://$NODE_IP:$NODE_PORT/"

{{- else if eq .Values.service.type "LoadBalancer" }}

** Please ensure an external IP is associated to the {{ template "bkauth.serviceName" . }} service before proceeding **
** Watch the status using: kubectl get svc --namespace {{ .Release.Namespace }} -w {{ template "bkauth.serviceName" . }} **

  export SERVICE_IP=$(kubectl get svc --namespace {{ .Release.Namespace }} {{ template "bkauth.serviceName" . }} --template "{{"{{ range (index .status.loadBalancer.ingress 0) }}{{.}}{{ end }}"}}")

{{- $port:=.Values.service.port | toString }}
  echo "bkauth URL: http://$SERVICE_IP{{- if ne $port "80" }}:{{ .Values.service.port }}{{ end }}/"
{{- else if eq .Values.service.type "ClusterIP" }}

  echo "bkauth URL: http://127.0.0.1:8080/"
  kubectl port-forward --namespace {{ .Release.Namespace }} svc/{{ template "bkauth.serviceName" . }} 8080:{{ .Values.service.port }}

{{- end }}
{{- end }}
