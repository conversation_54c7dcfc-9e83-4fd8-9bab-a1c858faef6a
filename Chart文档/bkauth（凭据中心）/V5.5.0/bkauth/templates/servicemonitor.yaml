{{- if .Values.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ template "bkauth.fullname" . }}
  {{- if .Values.serviceMonitor.namespace }}
  namespace: {{ .Values.serviceMonitor.namespace }}
  {{- else }}
  namespace: {{ .Release.Namespace }}
  {{- end }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.serviceMonitor.jobLabel }}
  jobLabel: {{ .Values.serviceMonitor.jobLabel }}
  {{- end }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" . | nindent 6 }}
      {{- if .Values.serviceMonitor.selector }}
      {{- include "common.tplvalues.render" (dict "value" .Values.serviceMonitor.selector "context" $) | nindent 6 }}
      {{- end }}
  endpoints:
    - port: http
      path: "/metrics"
      {{- if .Values.serviceMonitor.interval }}
      interval: {{ .Values.serviceMonitor.interval }}
      {{- end }}
      {{- if .Values.serviceMonitor.scrapeTimeout }}
      scrapeTimeout: {{ .Values.serviceMonitor.scrapeTimeout }}
      {{- end }}
      {{- if hasKey .Values.serviceMonitor "honorLabels" }}
      honorLabels: {{ .Values.serviceMonitor.honorLabels }}
      {{- end }}
      {{- if .Values.serviceMonitor.relabelings }}
      relabelings: {{- include "common.tplvalues.render" ( dict "value" .Values.serviceMonitor.relabelings "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.serviceMonitor.metricRelabelings }}
      metricRelabelings: {{- include "common.tplvalues.render" ( dict "value" .Values.serviceMonitor.metricRelabelings "context" $) | nindent 8 }}
      {{- end }}
  namespaceSelector:
    matchNames:
    - {{ .Release.Namespace }}
{{- end }}
