apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "bkauth.fullname" . }}-dbconfig
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: migration
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
data:
  dbconfig.yml: |
    production:
        dialect: mysql
        datasource: ${MYSQL_USER}:${MYSQL_PASSWORD}@tcp(${MYSQL_HOST}:${MYSQL_PORT})/${MYSQL_NAME}?parseTime=true
        dir: /app/sql
