apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "bkauth.fullname" . }}-config
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
data:
  config.yaml: |-
    debug: {{ .Values.debug }}
    crypto:
      key: {{ default "G7kR9mT2sVbXfWqP" .Values.global.crypto.key }}
      mark: {{ default "ENC" .Values.global.crypto.mark }}

    server:
      host: 
      port: {{ .Values.containerPort }}

      readTimeout: 300
      writeTimeout: 300
      idleTimeout: 180

    sentry:
      {{- if .Values.sentryDsn }}
      enable: true
      {{- else }}
      enable: false
      {{- end }}
      dsn: "{{ .Values.sentryDsn }}"

    pprofPassword: {{ randAlphaNum 16 | quote}}

    encryptKey: "{{ .Values.encryptKey }}"

    accessKeys:
      {{- toYaml .Values.accessKeys | nindent 6 }}

    apiAllowLists:
      - api: "manage_app"
        {{- if .Values.manageAppAPIAllowList }}
        allowList: "{{ .Values.manageAppAPIAllowList }}"
        {{- else }}
        allowList: "bk_paas,bk_paas3"
        {{- end }}
      - api: "manage_access_key"
        {{- if .Values.manageAccessKeyAPIAllowList }}
        allowList: "{{ .Values.manageAccessKeyAPIAllowList }}"
        {{- else }}
        allowList: "bk_paas,bk_paas3"
        {{- end }}
      - api: "read_access_key"
        {{- if .Values.readAccessKeyAPIAllowList }}
        allowList: "{{ .Values.readAccessKeyAPIAllowList }}"
        {{- else }}
        allowList: "bk_paas,bk_paas3,bk_apigateway"
        {{- end }}
      - api: "verify_secret"
        {{- if .Values.verifySecretAPIAllowList }}
        allowList: "{{ .Values.verifySecretAPIAllowList }}"
        {{- else }}
        allowList: "bk_paas,bk_paas3,bk_apigateway,bk_iam,bk_ssm"
        {{- end }}

    databases:
      - id: "bkauth"
        host: "{{ template "bkauth.databaseHost" . }}"
        port: {{ template "bkauth.databasePort" . }}
        user: "{{ template "bkauth.databaseUser" . }}"
        password: "{{ template "bkauth.databasePassword" . }}"
        name: "{{ template "bkauth.databaseName" . }}"
        maxOpenConns: 1000
        maxIdleConns: 100
        connMaxLifetimeSecond: 300
      {{- if .Values.externalDatabase.openPaas.enabled }}
      - id: "open_paas"
        host: "{{ template "bkauth.openPaas.databaseHost" . }}"
        port: {{ template "bkauth.openPaas.databasePort" . }}
        user: "{{ template "bkauth.openPaas.databaseUser" . }}"
        password: "{{ template "bkauth.openPaas.databasePassword" . }}"
        name: "{{ template "bkauth.openPaas.databaseName" . }}"
      {{- end }}
    redis:
      {{- if and (not .Values.redis.enabled) .Values.externalRedis.default.sentinel.enabled }}
      - id: "sentinel"
      {{- else }}
      - id: "standalone"
      {{- end }}
        addr: "{{ template "bkauth.redis.host" . }}:{{ template "bkauth.redis.port" . }}"
        password: "{{ template "bkauth.redis.password" . }}"
        db: {{ template "bkauth.redis.db" . }}
        poolSize: 300
        dialTimeout: 3
        readTimeout: 1
        writeTimeout: 1
        {{- if and (not .Values.redis.enabled) .Values.externalRedis.default.sentinel.enabled }}
        sentinelAddr: "{{ .Values.externalRedis.default.sentinel.hosts }}"
        masterName: "{{ .Values.externalRedis.default.sentinel.masterSet }}"
        sentinelPassword: "{{ .Values.externalRedis.default.sentinel.password }}"
        {{- else }}
        sentinelAddr: ""
        masterName: ""
        sentinelPassword: ""
        {{- end }}

    logger:
      {{- if or (eq .Values.bkLogConfig.enabled true) (eq .Values.logWriter "file") }}
      system:
        level: info
        encoding: console
        writer: file
        settings: {name: bkauth.log, size: 100, backups: 10, age: 7, path: /app/logs/}
      api:
        level: info
        encoding: json
        writer: file
        settings: {name: bkauth_api.log, size: 100, backups: 10, age: 7, path: /app/logs/}
      sql:
        level: info
        encoding: json
        writer: file
        settings: {name: bkauth_sql.log, size: 100, backups: 10, age: 7, path: /app/logs/}
      audit:
        level: info
        encoding: json
        writer: file
        settings: {name: bkauth_audit.log, size: 500, backups: 20, age: 365, path: /app/logs/}
      web:
        level: info
        encoding: json
        writer: file
        settings: {name: bkauth_web.log, size: 100, backups: 10, age: 7, path: /app/logs/}
      component:
        level: error
        encoding: json
        writer: file
        settings: {name: bkauth_component.log, size: 100, backups: 10, age: 7, path: /app/logs/}
      {{- else }}
      system:
        level: info
        encoding: console
        writer: os
        settings: {name: stdout}
      api:
        level: info
        encoding: json
        writer: os
        settings: {name: stdout}
      sql:
        level: info
        encoding: json
        writer: os
        settings: {name: stdout}
      audit:
        level: info
        encoding: json
        writer: os
        settings: {name: stdout}
      web:
        level: info
        encoding: json
        writer: os
        settings: {name: stdout}
      component:
        level: error
        encoding: json
        writer: os
        settings: {name: stdout}
      {{- end }}
