{{/* vim: set filetype=mustache: */}}
{{/*
Expand the name of the chart.
*/}}
{{- define "bkauth.name" -}}
{{- include "common.names.name" . -}}
{{- end -}}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
*/}}
{{- define "bkauth.fullname" -}}
{{- include "common.names.fullname" . -}}
{{- end -}}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "bkauth.chart" -}}
{{- include "common.names.chart" . -}}
{{- end -}}

{{/*
Return the proper bkauth image name
*/}}
{{- define "bkauth.image" -}}
{{ include "common.images.image" (dict "imageRoot" .Values.image "global" .Values.global) }}
{{- end -}}

{{/*
Return the proper Docker Image Registry Secret Names
*/}}
{{- define "bkauth.imagePullSecrets" -}}
{{ include "common.images.pullSecrets" (dict "images" (list .Values.image) "global" .Values.global) }}
{{- end -}}

{{/*
 Create the name of the service account to use
 */}}
{{- define "bkauth.serviceAccountName" -}}
{{- if .Values.serviceAccount.create -}}
    {{ default (include "bkauth.fullname" .) .Values.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{/*
Return true if cert-manager required annotations for TLS signed certificates are set in the Ingress annotations
Ref: https://cert-manager.io/docs/usage/ingress/#supported-annotations
*/}}
{{- define "bkauth.ingress.certManagerRequest" -}}
{{ if or (hasKey . "cert-manager.io/cluster-issuer") (hasKey . "cert-manager.io/issuer") }}
    {{- true -}}
{{- end -}}
{{- end -}}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
*/}}
{{- define "bkauth.mariadb.fullname" -}}
{{- if .Values.mariadb.fullnameOverride -}}
  {{- .Values.mariadb.fullnameOverride | trunc 63 | trimSuffix "-" -}}
{{- else -}}
  {{- printf "%s-mariadb" .Release.Name | trunc 63 | trimSuffix "-" -}}
{{- end -}}
{{- end -}}

{{/*
Return the MariaDB Hostname
*/}}
{{- define "bkauth.databaseHost" -}}
{{- if .Values.mariadb.enabled }}
    {{- if eq .Values.mariadb.architecture "replication" }}
        {{- printf "%s-primary" (include "bkauth.mariadb.fullname" .) | trunc 63 | trimSuffix "-" -}}
    {{- else -}}
        {{- printf "%s" (include "bkauth.mariadb.fullname" .) -}}
    {{- end -}}
{{- else -}}
    {{- printf "%s" .Values.externalDatabase.default.host -}}
{{- end -}}
{{- end -}}

{{/*
Return the MariaDB Port
*/}}
{{- define "bkauth.databasePort" -}}
{{- if .Values.mariadb.enabled }}
    {{- printf "3306" -}}
{{- else -}}
    {{- printf "%d" (.Values.externalDatabase.default.port | int ) -}}
{{- end -}}
{{- end -}}

{{/*
Return the MariaDB Database Name
*/}}
{{- define "bkauth.databaseName" -}}
{{- if .Values.mariadb.enabled }}
    {{- printf "%s" .Values.mariadb.auth.database -}}
{{- else -}}
    {{- printf "%s" .Values.externalDatabase.default.database -}}
{{- end -}}
{{- end -}}

{{/*
Return the MariaDB User
*/}}
{{- define "bkauth.databaseUser" -}}
{{- if .Values.mariadb.enabled }}
    {{- printf "%s" .Values.mariadb.auth.username -}}
{{- else -}}
    {{- printf "%s" .Values.externalDatabase.default.user -}}
{{- end -}}
{{- end -}}

{{/*
Return the MariaDB Password
*/}}
{{- define "bkauth.databasePassword" -}}
{{- if .Values.mariadb.enabled }}
  {{- printf "%s" .Values.mariadb.auth.password -}}
{{- else -}}
  {{- printf "%s" .Values.externalDatabase.default.password -}}
{{- end -}}
{{- end -}}

{{/*
Create a default fully qualified redis name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
*/}}
{{- define "bkauth.redis.fullname" -}}
{{- if .Values.redis.fullnameOverride -}}
  {{- printf "%s-master" .Values.redis.fullnameOverride | trunc 63 | trimSuffix "-" -}}
{{- else -}}
  {{- $name := default "redis" .Values.redis.nameOverride -}}
  {{- printf "%s-%s-master" .Release.Name $name | trunc 63 | trimSuffix "-" -}}
{{- end -}}
{{- end -}}

{{- define "bkauth.redis.host" -}}
  {{- if eq .Values.redis.enabled true -}}
    {{- template "bkauth.redis.fullname" . -}}
  {{- else -}}
    {{- .Values.externalRedis.default.host -}}
  {{- end -}}
{{- end -}}

{{- define "bkauth.redis.port" -}}
  {{- if eq .Values.redis.enabled true -}}
    {{- printf "%s" "6379" -}}
  {{- else -}}
    {{- .Values.externalRedis.default.port -}}
  {{- end -}}
{{- end -}}

{{- define "bkauth.redis.db" -}}
  {{- if eq .Values.redis.enabled true -}}
    {{- printf "%s" "0" -}}
  {{- else -}}
    {{- .Values.externalRedis.default.db -}}
  {{- end -}}
{{- end -}}

{{/*
Get the Redis(TM) Password.
*/}}
{{- define "bkauth.redis.password" -}}
{{- if .Values.redis.enabled }}
  {{- printf "%s" .Values.redis.auth.password -}}
{{- else -}}
  {{- .Values.externalRedis.default.password -}}
{{- end -}}
{{- end -}}

{{/*
Return the proper bkauth migration image
{{- include "bkauth.migration.image" (dict "image" .Values.migration.images.busybox "imageRoot" .Values.image) -}}
*/}}
{{- define "bkauth.migration.image" -}}
{{- $registryName := .image.registry -}}
{{- if not .image.registry -}}
  {{- $registryName = .imageRoot.registry -}}
{{- end -}}
{{- $repositoryName := .image.repository -}}
{{- $tag := .image.tag | toString -}}
{{- if $registryName }}
{{- printf "%s/%s:%s" $registryName $repositoryName $tag -}}
{{- else -}}
{{- printf "%s:%s" $repositoryName $tag -}}
{{- end -}}
{{- end -}}

{{/*
Return the proper bkauth migration busybox image name
*/}}
{{- define "bkauth.migration.busybox.image" -}}
{{- if and .Values.global .Values.global.imageRegistry -}}
  {{- include "common.images.image" (dict "imageRoot" .Values.migration.images.busybox "global" .Values.global) -}}
{{- else -}}
  {{- include "bkauth.migration.image" (dict "image" .Values.migration.images.busybox "imageRoot" .Values.image) -}}
{{- end -}}
{{- end -}}

{{/*
Return the proper bkauth migration k8sWaitFor image name
*/}}
{{- define "bkauth.migration.k8sWaitFor.image" -}}
{{- if and .Values.global .Values.global.imageRegistry -}}
  {{- include "common.images.image" (dict "imageRoot" .Values.migration.images.k8sWaitFor "global" .Values.global) -}}
{{- else -}}
  {{- include "bkauth.migration.image" (dict "image" .Values.migration.images.k8sWaitFor "imageRoot" .Values.image) -}}
{{- end -}}
{{- end -}}

{{/*
Return the OpenPaas Hostname
*/}}
{{- define "bkauth.openPaas.databaseHost" -}}
{{- if not .Values.externalDatabase.openPaas.enabled }}
    {{- if eq .Values.mariadb.architecture "replication" }}
        {{- printf "%s-primary" (include "bkauth.mariadb.fullname" .) | trunc 63 | trimSuffix "-" -}}
    {{- else -}}
        {{- printf "%s" (include "bkauth.mariadb.fullname" .) -}}
    {{- end -}}
{{- else -}}
    {{- printf "%s" .Values.externalDatabase.openPaas.host -}}
{{- end -}}
{{- end -}}

{{/*
Return the OpenPaas Port
*/}}
{{- define "bkauth.openPaas.databasePort" -}}
{{- if not .Values.externalDatabase.openPaas.enabled }}
    {{- printf "3306" -}}
{{- else -}}
    {{- printf "%d" (.Values.externalDatabase.openPaas.port | int ) -}}
{{- end -}}
{{- end -}}

{{/*
Return the OpenPaas Database Name
*/}}
{{- define "bkauth.openPaas.databaseName" -}}
{{- if not .Values.externalDatabase.openPaas.enabled }}
    {{- printf "%s" "open_paas" -}}
{{- else -}}
    {{- printf "%s" .Values.externalDatabase.openPaas.database -}}
{{- end -}}
{{- end -}}

{{/*
Return the OpenPaas User
*/}}
{{- define "bkauth.openPaas.databaseUser" -}}
{{- if not .Values.externalDatabase.openPaas.enabled }}
    {{- printf "%s" .Values.mariadb.auth.username -}}
{{- else -}}
    {{- printf "%s" .Values.externalDatabase.openPaas.user -}}
{{- end -}}
{{- end -}}

{{/*
Return the OpenPaas Password
*/}}
{{- define "bkauth.openPaas.databasePassword" -}}
{{- if not .Values.externalDatabase.openPaas.enabled }}
  {{- printf "%s" .Values.mariadb.auth.password -}}
{{- else -}}
  {{- printf "%s" .Values.externalDatabase.openPaas.password -}}
{{- end -}}
{{- end -}}

{{/*
Expand the name of the service.
*/}}
{{- define "bkauth.serviceName" -}}
{{- printf "%s" (include "bkauth.fullname" .) -}}
{{- end -}}
