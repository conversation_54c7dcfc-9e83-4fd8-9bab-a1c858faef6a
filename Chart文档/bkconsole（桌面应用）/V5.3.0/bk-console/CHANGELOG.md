# 版本历史

## v0.1.3-beta.3
- initContainers增加resource

## v0.1.3-beta.2
- 个人信息脱敏展示
- 启动命令保证静态资源收集完成后再执行

## v0.1.3-beta.1
- 无权限用户跳转到页面提示错误信息
 

## v0.1.2-beta.1
- 用户信息直接从 API 中获取,不再存储到 DB 中
- 用户创建后，is_superuser 信息不再从 API 中同步
- 应用详情中不再展示”已添加用户“


## 0.1.1-beta.6
- fix: 国际化体验问题修复

## 0.1.1-beta.5
- fix: 修复市场2个位置开发者展示逻辑不一致
- fix: 修复解析微信公众号签名报错
- feat: esbchannel 的配置从 API 中获取

## 0.1.1-beta.3
- 修复无法新建文件问题
- 用API的错误日志中添加 request_id


## 0.1.0
- 发布 0.1.0

## 0.1.0-beta12
- 修复应用市场中最近打开项中未展示新标签页打开的应用的问题

## 0.1.0-beta11
- 去掉活跃度统计
- dock栏中去掉用户管理

## 0.1.0-beta10
- 修复配置esb管道中的微信信息导致个人中心报错

## 0.1.0-beta9
- 去掉QQ咨询入口
- admin中更新应用信息后logo路径问题修复

## 0.1.0-beta8
- 版本号从环境变量中获取
- [charts] 新增配置项 bkVersion , 代表蓝鲸版本号

## 0.1.0-beta7
- title 修正
- 修复 add_app_to_desktop 命令重置应用 Logo 问题

## 0.1.0-beta6
- 桌面壁纸显示方式默认为拉伸
- 修复注销时点击取消报错
- 新增命令添加应用到用户桌面命令 add_app_to_desktop
```
# 注意: 若用户未打开桌面之前就执行了该命令将部分应用添加到用户桌面，说明用户已经初始化桌面数据，用户首次打开桌面时，不会再初始化默认应用到桌面
python manage.py add_app_to_desktop --app_code="" --username=""
```

## 0.1.0-beta5
- 支持 ipv6 部署

## 0.1.0-beta4
- 修复配置平台、作业平台地址未初始化问题
- 配置平台、作业平台用新标签页打开
- Python 版本升级到 3.8, Django 版本升级到 3.2
- [charts] 新增配置平台、作业平台 addr 配置项
```
bkCmdbAddr: cmdb.example.com
bkJobAddr: job.example.com
```

## 0.1.0-beta3
- 补充 migration 文件

## 0.1.0-beta2
- 支持从根路径自动跳转到 /console 路径

## 0.1.0-beta1
- 添加初始化 open_paas 表的 migration 操作
- 添加 metric 指标

## 0.0.16
- 修复敏感信息

## 0.0.15
- 修复敏感信息

## 0.0.14
- 修复个人中心修改密码地址无效问题
- 通过 ESB 调用用户管理 API
  - 依赖 bk-apigateway ( charts:0.4.27 , appVersion: 1.1.14) 版本
- [charts] 新增配置项：bkUserUrl, 用于个人中心修改密码跳转链接
- [charts] 删除配置项：bkUserApiUrl, 用于个人中心修改密码跳转链接


## 0.0.13
- 日志采集配置修复

## 0.0.12
- 修改 title
- 产品文档链接改为蓝鲸官网文档地址