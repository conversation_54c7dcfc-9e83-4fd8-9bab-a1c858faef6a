{{/* vim: set filetype=mustache: */}}
{{/*
Expand the name of the chart.
*/}}
{{- define "bk-console.name" -}}
{{- include "common.names.name" . -}}
{{- end -}}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
*/}}
{{- define "bk-console.fullname" -}}
{{- include "common.names.fullname" . -}}
{{- end -}}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "bk-console.chart" -}}
{{- include "common.names.chart" . -}}
{{- end -}}

{{/*
Return the proper bk-console image name
*/}}
{{- define "bk-console.image" -}}
{{ include "common.images.image" (dict "imageRoot" .Values.image "global" .Values.global) }}
{{- end -}}

{{/*
Return the proper Docker Image Registry Secret Names
*/}}
{{- define "bk-console.imagePullSecrets" -}}
{{ include "common.images.pullSecrets" (dict "images" (list .Values.image) "global" .Values.global) }}
{{- end -}}

{{/*
 Create the name of the service account to use
 */}}
{{- define "bk-console.serviceAccountName" -}}
{{- if .Values.serviceAccount.create -}}
    {{ default (include "bk-console.fullname" .) .Values.serviceAccount.name }}
{{- else -}}
    {{ default "default" .Values.serviceAccount.name }}
{{- end -}}
{{- end -}}

{{/*
Return true if cert-manager required annotations for TLS signed certificates are set in the Ingress annotations
Ref: https://cert-manager.io/docs/usage/ingress/#supported-annotations
*/}}
{{- define "bk-console.ingress.certManagerRequest" -}}
{{ if or (hasKey . "cert-manager.io/cluster-issuer") (hasKey . "cert-manager.io/issuer") }}
    {{- true -}}
{{- end -}}
{{- end -}}

{{/*
Expand the name of the service.
*/}}
{{- define "bk-console.serviceName" -}}
{{- printf "%s-web" (include "bk-console.fullname" .) -}}
{{- end -}}

{{/*
Return url from bk-console ingress
*/}}
{{- define "bk-console.ingress.url" -}}
{{- $schema := "http://" -}}
{{- if .Values.ingress.tls -}}
  {{- $schema = "https://" -}}
{{- end -}}
{{- printf "%s%s%s" $schema .Values.ingress.hostname .Values.ingress.path -}}
{{- end -}}


{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
*/}}
{{- define "bk-console.mariadb.fullname" -}}
{{- if .Values.mariadb.fullnameOverride -}}
  {{- .Values.mariadb.fullnameOverride | trunc 63 | trimSuffix "-" -}}
{{- else -}}
  {{- printf "%s-mariadb" .Release.Name | trunc 63 | trimSuffix "-" -}}
{{- end -}}
{{- end -}}

{{/*
Return the MariaDB Hostname
*/}}
{{- define "bk-console.databaseHost" -}}
{{- if .Values.mariadb.enabled }}
    {{- if eq .Values.mariadb.architecture "replication" }}
        {{- printf "%s-primary" (include "bk-console.mariadb.fullname" .) | trunc 63 | trimSuffix "-" -}}
    {{- else -}}
        {{- printf "%s" (include "bk-console.mariadb.fullname" .) -}}
    {{- end -}}
{{- else -}}
    {{- printf "%s" .Values.externalDatabase.default.host -}}
{{- end -}}
{{- end -}}

{{/*
Return the MariaDB Port
*/}}
{{- define "bk-console.databasePort" -}}
{{- if .Values.mariadb.enabled }}
    {{- printf "3306" -}}
{{- else -}}
    {{- printf "%d" (.Values.externalDatabase.default.port | int ) -}}
{{- end -}}
{{- end -}}

{{/*
Return the MariaDB Database Name
*/}}
{{- define "bk-console.databaseName" -}}
{{- if .Values.mariadb.enabled }}
    {{- printf "%s" .Values.mariadb.auth.database -}}
{{- else -}}
    {{- printf "%s" .Values.externalDatabase.default.database -}}
{{- end -}}
{{- end -}}

{{/*
Return the MariaDB User
*/}}
{{- define "bk-console.databaseUser" -}}
{{- if .Values.mariadb.enabled }}
    {{- printf "%s" .Values.mariadb.auth.username -}}
{{- else -}}
    {{- printf "%s" .Values.externalDatabase.default.user -}}
{{- end -}}
{{- end -}}

{{/*
Return the MariaDB Password
*/}}
{{- define "bk-console.databasePassword" -}}
{{- if .Values.mariadb.enabled }}
  {{- printf "%s" .Values.mariadb.auth.password -}}
{{- else -}}
  {{- printf "%s" .Values.externalDatabase.default.password -}}
{{- end -}}
{{- end -}}


{{/*
Return the proper bk-console migration image
{{- include "bk-console.migration.image" (dict "image" .Values.migration.busybox.image "imageRoot" .Values.image) -}}
*/}}
{{- define "bk-console.migration.image" -}}
{{- $registryName := .image.registry -}}
{{- if not .image.registry -}}
  {{- $registryName = .imageRoot.registry -}}
{{- end -}}
{{- $repositoryName := .image.repository -}}
{{- $tag := .image.tag | toString -}}
{{- if $registryName }}
{{- printf "%s/%s:%s" $registryName $repositoryName $tag -}}
{{- else -}}
{{- printf "%s:%s" $repositoryName $tag -}}
{{- end -}}
{{- end -}}

{{/*
Return the proper bk-console migration busybox image name
*/}}
{{- define "bk-console.migration.busybox.image" -}}
{{- if and .Values.global .Values.global.imageRegistry -}}
  {{- include "common.images.image" (dict "imageRoot" .Values.migration.images.busybox "global" .Values.global) -}}
{{- else -}}
  {{- include "bk-console.migration.image" (dict "image" .Values.migration.images.busybox "imageRoot" .Values.image) -}}
{{- end -}}
{{- end -}}

{{/*
Return the proper bk-console migration k8sWaitFor image name
*/}}
{{- define "bk-console.migration.k8sWaitFor.image" -}}
{{- if and .Values.global .Values.global.imageRegistry -}}
  {{- include "common.images.image" (dict "imageRoot" .Values.migration.images.k8sWaitFor "global" .Values.global) -}}
{{- else -}}
  {{- include "bk-console.migration.image" (dict "image" .Values.migration.images.k8sWaitFor "imageRoot" .Values.image) -}}
{{- end -}}
{{- end -}}


{{/*
Return the env of bk-console image
*/}}
{{- define "bk-console.imageEnvVars" -}}
- name: BK_PAAS_SECRET_KEY
  value: {{ .Values.appSecret }}
- name: BK_DOMAIN
  value: {{ .Values.bkDomain }}
- name: BK_LOGIN_API_URL
  value: {{ .Values.bkLoginApiUrl }}
- name: BK_COMPONENT_API_URL
  value: {{ .Values.bkComponentApiUrl }}
- name: BK_IAM_API_URL
  value: {{ .Values.bkIamBackendUrl }}
- name: BK_PAAS_PUBLIC_ADDR
  value: {{ .Values.ingress.hostname }}
- name: SENTRY_DSN
  value: {{ .Values.sentryDsn | quote}}
- name: BK_PAAS_DATABASE_HOST
  value: {{ template "bk-console.databaseHost" . }}
- name: BK_PAAS_DATABASE_PORT
  value: {{ include "bk-console.databasePort" . | quote }}
- name: BK_PAAS_DATABASE_NAME
  value: {{ template "bk-console.databaseName" . }}
- name: BK_PAAS_DATABASE_USER
  value: {{ template "bk-console.databaseUser" . }}
- name: BK_PAAS_DATABASE_PASSWORD
  value: {{ include "bk-console.databasePassword" . | quote }}
- name: IS_BK_SUITE_ENABLED
  value: {{ .Values.externalDatabase.bkSuite.enabled | quote}}
- name: BK_SUITE_DATABASE_HOST
  value: {{ .Values.externalDatabase.bkSuite.host | quote}}
- name: BK_SUITE_DATABASE_PORT
  value: {{ .Values.externalDatabase.bkSuite.port | quote}}
- name: BK_SUITE_DATABASE_NAME
  value: {{ .Values.externalDatabase.bkSuite.database | quote}}
- name: BK_SUITE_DATABASE_USER
  value: {{ .Values.externalDatabase.bkSuite.user | quote}}
- name: BK_SUITE_DATABASE_PASSWORD
  value: {{ .Values.externalDatabase.bkSuite.password | quote}}
- name: BK_PAAS3_URL
  value: {{ .Values.bkPaas3Url }}
- name: BK_USER_URL
  value: {{ .Values.bkUserUrl }}
- name: BK_PAAS_HTTP_SCHEMA
  value: {{ .Values.bkDomainScheme }}
- name: BK_CMDB_ADDR
  value: {{ .Values.bkCmdbAddr }}
- name: BK_JOB_ADDR
  value: {{ .Values.bkJobAddr }}
- name: BK_VERSION
  value: {{ .Values.bkVersion | quote}}
{{- end -}}
