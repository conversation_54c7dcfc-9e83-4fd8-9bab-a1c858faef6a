{{- $host := include "mariadb.primary.fullname" . }}
{{- $port := print .Values.primary.service.ports.mysql }}
{{- $rootPassword := include "common.secrets.passwords.manage" (dict "secret" (include "mariadb.secretName" .) "key" "mariadb-root-password" "providedValues" (list "auth.rootPassword") "context" $) | trimAll "\"" | b64dec }}
{{- $password := include "common.secrets.passwords.manage" (dict "secret" (include "mariadb.secretName" .) "key" "mariadb-password" "providedValues" (list "auth.password") "context" $) | trimAll "\"" | b64dec }}
{{- if eq (include "mariadb.createSecret" .) "true" }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if (not .Values.auth.forcePassword) }}
  mariadb-root-password: {{ print $rootPassword | b64enc | quote }}
  {{- else }}
  mariadb-root-password: {{ required "A MariaDB Root Password is required!" .Values.auth.rootPassword | b64enc | quote }}
  {{- end }}
  {{- if (not (empty .Values.auth.username)) }}
  {{- if (not .Values.auth.forcePassword) }}
  mariadb-password: {{ print $password | b64enc | quote }}
  {{- else }}
  mariadb-password: {{ required "A MariaDB Database Password is required!" .Values.auth.password | b64enc | quote }}
  {{- end }}
  {{- end }}
  {{- if eq .Values.architecture "replication" }}
  {{- if (not .Values.auth.forcePassword) }}
  mariadb-replication-password: {{ include "common.secrets.passwords.manage" (dict "secret" (include "common.names.fullname" .) "key" "mariadb-replication-password" "providedValues" (list "auth.replicationPassword") "context" $) }}
  {{- else }}
  mariadb-replication-password: {{ required "A MariaDB Replication Password is required!" .Values.auth.replicationPassword | b64enc | quote }}
  {{- end }}
  {{- end }}
{{- end }}

{{- if .Values.serviceBindings.enabled }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}-svcbind-root
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: servicebinding.io/mysql
data:
  provider: {{ print "bitnami" | b64enc | quote }}
  type: {{ print "mysql" | b64enc | quote }}
  host: {{ print $host | b64enc | quote }}
  port: {{ print $port | b64enc | quote }}
  user: {{ print "root" | b64enc | quote }}
  password: {{ print $rootPassword | b64enc | quote }}
  uri: {{ printf "mysql://root:%s@%s:%s" $rootPassword $host $port | b64enc | quote }}

{{- if .Values.auth.username }}
{{- $database := .Values.auth.database  }}
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}-svcbind-custom-user
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: servicebinding.io/mysql
data:
  provider: {{ print "bitnami" | b64enc | quote }}
  type: {{ print "mysql" | b64enc | quote }}
  host: {{ print $host | b64enc | quote }}
  port: {{ print $port | b64enc | quote }}
  user: {{ print .Values.auth.username | b64enc | quote }}
  {{- if $database }}
  database: {{ print $database | b64enc | quote }}
  {{- end }}
  password: {{ print $password | b64enc | quote }}
  uri: {{ printf "mysql://%s:%s@%s:%s/%s" .Values.auth.username $password $host $port $database | b64enc | quote }}
{{- end }}
{{- end }}
