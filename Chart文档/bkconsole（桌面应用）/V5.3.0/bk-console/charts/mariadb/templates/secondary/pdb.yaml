{{- if and (eq .Values.architecture "replication") .Values.secondary.pdb.create }}
apiVersion: {{ include "common.capabilities.policy.apiVersion" . }}
kind: PodDisruptionBudget
metadata:
  name: {{ include "mariadb.secondary.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: secondary
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.secondary.pdb.minAvailable }}
  minAvailable: {{ .Values.secondary.pdb.minAvailable }}
  {{- end }}
  {{- if .Values.secondary.pdb.maxUnavailable }}
  maxUnavailable: {{ .Values.secondary.pdb.maxUnavailable }}
  {{- end }}
  selector:
    matchLabels: {{ include "common.labels.matchLabels" . | nindent 6 }}
      app.kubernetes.io/component: secondary
{{- end }}
