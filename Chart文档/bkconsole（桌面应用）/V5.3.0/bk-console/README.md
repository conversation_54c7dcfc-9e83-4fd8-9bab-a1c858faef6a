# bk-console

bk-console 是蓝鲸桌面。本文档内容为如何在 Kubernetes 集群上部署 bk-console 服务。

## 准备服务依赖

开始部署前，请准备好一套 Kubernetes 集群（版本 1.12 或更高），并安装 Helm 命令行工具（3.0 或更高版本）。

> 注：如使用 BCS 容器服务部署，可用 BCS 的图形化 Helm 功能替代 Helm 命令行。

我们使用 `Ingress` 对外提供服务访问，所以请在集群中至少安装一个可用的 [Ingress Controller](https://kubernetes.io/docs/concepts/services-networking/ingress-controllers/)


### 其他服务

要正常运行 bk-console，需要用到一些蓝鲸体系内的其他服务，它们是：

- 蓝鲸统一登录：依赖其进行登录操作

以下依赖非必须，但缺少会影响工作台部分功能：
- 蓝鲸用户管理：依赖其个人中心的个人用户信息
- 蓝鲸权限中心：依赖其校验权限信息

准备好依赖服务后，下一步是编写 `values.yaml` 配置文件。

## 准备 `values.yaml`

bk-console  无法直接通过 Chart 所提供的默认 [values.yaml](values.yaml) 完成部署，在执行 `helm install` 安装服务前，你必须按以下步骤准备好匹配当前部署环境的 `values.yaml`。

### 1. 配置镜像地址

编写配置文件的第一步，是将 `image.registry` 配置为你所使用的镜像源地址。

我们会在每次发布版本时，会同步更新 Chart 中的镜像版本。如果你想使用其他版本或者自己构建的镜像，也可以在 `values.yaml` 中修改，配置示例：

##### `values.yaml` 配置示例：

```yaml
image:
  registry: "hub.bktencent.com"
  repository: blueking/bk-console
  tag: 1.0.0
```

> 注：假如服务镜像需凭证才能拉取。请将对应密钥名称写入配置文件中，详细请查看 `global.imagePullSecrets` 配置项说明。

### 2. 配置桌面的访问域名

桌面的访问域名必须要保证能够获取到蓝鲸统一登录写入的登录 cookie。

##### `values.yaml` 配置示例：

```yaml
ingress:
  hostname: paas.example.com
```

### 3. 配置蓝鲸体系的依赖项

需要配置：
1. 应用（bk_paas）对应的 bk_app_secret
2. 蓝鲸登录态 cookie 的访问域
3. 蓝鲸统一登录服务的访问域名（注意：这里只需要填写域名即可）
4. ESB API 地址

其他依赖的蓝鲸产品（如 ESB、用户管理、权限中心）默认用同命名空间的 svc 地址访问。

##### `values.yaml` 配置示例：
```yaml
# 蓝鲸版本号
bkVersion: ""

## 应用(bk_paas) 对应的 bk_app_secret, 与 ESB 的通信凭证
appSecret: ""

## cookie 访问域
bkDomain: "example.com"

## 浏览器访问协议
bkDomainScheme: "http"

## PaaS3.0 开发者中心访问地址
bkPaas3Url: "http://bkpaas.example.com"

## ESB API 地址
bkComponentApiUrl: "http://bkapi.example.com"

## 统一登录服务API访问地址 (默认为同命名空间下的 svc 地址)
bkLoginApiUrl: "http://bk-login-web"

## 用户管理访问地址
bkUserUrl: "http://bkuser.example.com"

## bkiam 后端 url（默认为同命名空间下的 svc 地址）
bkIamBackendUrl: http://bkiam-web

## 蓝鲸 cmdb addr
bkCmdbAddr: cmdb.example.com

## 蓝鲸 job addr
bkJobAddr: job.example.com

```

### 4. 蓝鲸日志采集配置

用于将容器日志和标准输出日志采集到蓝鲸日志平台。默认未开启，如需开启请将 `bkLogConfig.enabled` 设置为 true。

##### `values.yaml` 配置示例：
```yaml
## 蓝鲸日志采集
bkLogConfig:
  enabled: false
  dataId: 1
```

### 5. 容器监控 Service Monitor

默认未开启，如需开启请将 `serviceMonitor.enabled` 设置为 true。

##### `values.yaml` 配置示例：
```yaml
serviceMonitor:
  enabled: true
```

### 6. 数据库依赖

桌面依赖 PaaS.0 开发者中心创建的 open_paas 数据库。

##### `values.yaml` 配置示例：
```yaml
externalDatabase:
  default:
    ## @param externalDatabase.host External Database server host
    ##
    host: bk-console-mariadb
    ## @param externalDatabase.port External Database server port
    ##
    port: 3306
    ## @param externalDatabase.user External Database username
    ##
    user: bk_panel
    ## @param externalDatabase.password External Database user password
    ##
    password: "root"
    ## @param externalDatabase.database External Database database name
    ##
    database: open_paas
```

#### [可选]配置蓝鲸产品版本号数据库信息

用于在桌面上展示蓝鲸产品的版本号，`bkSuite.enabled=false` 则不展示产品版本相关信息。

##### `values.yaml` 配置示例：

```yaml
externalDatabase:
  bkSuite:
    ## 是否展示蓝鲸产品版本信息
    enabled: false
    ## bkSuite.enabled=true, 则需要配置下面相关的 DB 信息
    host: localhost
    ## @param externalDatabase.port External Database server port
    ##
    port: 3306
    ## @param externalDatabase.user External Database username
    ##
    user: bksuite
    ## @param externalDatabase.password External Database user password
    ##
    password: ""
    ## @param externalDatabase.database External Database database name
    ##
    database: bksuite_common
```

完成 `values.yaml` 的所有准备工作后，接下来就可以进行安装了。

### 安装Chart

填写 Values.yaml 后，要安装 BK-Panel，你必须先添加一个有效的 Helm repo 仓库。

```shell
# 请将 `<HELM_REPO_URL>` 替换为本 Chart 所在的 Helm 仓库地址
$ helm repo add bkrepo <HELM_REPO_URL>
```

添加仓库成功后，执行以下命令，在集群内安装名为 `bk-console` 的 Helm release（使用默认项目配置）：

```shell
$ helm install bk-console bkrepo/bk-console  --values values.yaml
```

上述命令将使用默认配置在 Kubernetes 集群中部署 bk-console , 并输出访问指引。

### 卸载Chart

使用以下命令卸载`bk-console`:

```shell
$ helm uninstall bk-console
```

上述命令将移除所有与 bk-console 相关的 Kubernetes 组件，并删除 release。


## 配置说明
[TODO]

### 如何修改配置项

在安装 Chart 时，你可以通过 `--set key=value[,key=value]` 的方式，在命令参数里修改各配置项。例如:

```shell
$ helm install bk-console bkrepo/bk-console  --set bkDomain ="example.com"
```

此外，你也可以把所有配置项写在 YAML 文件（常被称为 Helm values 文件）里，通过 `-f` 指定该文件来使用特定配置项：

```shell
$ helm install bk-console bkrepo/bk-console -f values.yaml
```

执行 `helm show values`，你可以查看 Chart 的所有默认配置：
```shell
# 查看默认配置
$ helm show values bkrepo/bk-console

# 保存默认配置到文件 values.yaml
$ helm show values bkrepo/bk-console > values.yaml
```

## 常见问题

[TODO]