#!/bin/bash

while getopts ":n:r:hx" opt; do
    case ${opt} in
        n)
            namespace=${OPTARG}
        ;;
        r)
            root=${OPTARG}
        ;;
        x)
            dry_run=1
        ;;
        h)
            echo "Usage: esb-custom-component-configmap-updater.sh -n <namespace> -r <component_dir> [-x]"
            echo
            echo "    -n: k8s namespace"
            echo "    -r: the custom component root directory, e.g. /path/to/components/generic/apis"
            echo "    -x: dry-run, only generate k8s yaml, do not apply"
            exit 0
        ;;
        \?)
            echo "Invalid option: -${OPTARG}" >&2
            exit 1
        ;;
        :)
            echo "Option -${OPTARG} requires an argument." >&2
            exit 1
        ;;
    esac
done

if [ ! -d "${root}" ]; then
    echo "Please specify the component root directory" >&2
    exit 1
fi

if [ -z "${namespace}" ]; then
    echo "Please specify the namespace" >&2
    exit 1
fi

# clean
rm -rf /tmp/bk_esb_custom_components_*.yaml || true

now_str=$(date +%Y%m%d%H%M)
bk_esb_custom_components_k8s_yaml="/tmp/bk_esb_custom_components_${now_str}.yaml"
bk_esb_custom_components_values_yaml="/tmp/bk_esb_custom_components_values_${now_str}.yaml"

touch "${bk_esb_custom_components_k8s_yaml}"

cat > "${bk_esb_custom_components_values_yaml}" <<EOF
bkEsb:
  customComponentConfigMaps:
EOF

cd "${root}" || exit 1

# handle python files, only files in directory components/generic/apis/, e.g. conf.py
find . -maxdepth 1 -type f -not -name '.*' -not -name '_*' -not -empty -name '*.py' | while read -r path; do
    path=$(sed -E 's/^\.\///;' <<< "${path}")
    name=esb.custompy.$(sed -E 's/_/-/g;' <<< "${path}")

    {
        echo "---"
        kubectl create configmap -o=yaml --dry-run=client -n="${namespace}" "${name}" --from-file="${path}"
    } >> "${bk_esb_custom_components_k8s_yaml}"

    cat >> "${bk_esb_custom_components_values_yaml}" <<EOF
    - configmap: "${name}"
      mountPath: "${path}"
      subPath: "${path}"
EOF
done

# handle custom components, only directory in directory components/generic/apis/
find . -type d -not -name '.*' -not -name '_*' -not -name '*_' -not -empty | while read -r path; do
    path=$(sed -E 's/^\.\///;' <<< "${path}")
    name=esb.custom.$(sed -E 's/_/-/g;s/\//./g;' <<< "${path}")

    {
        echo "---"
        kubectl create configmap -o=yaml --dry-run=client -n="${namespace}" "${name}" --from-file="${path}"
    } >> "${bk_esb_custom_components_k8s_yaml}"

    cat >> "${bk_esb_custom_components_values_yaml}" <<EOF
    - configmap: "${name}"
      mountPath: "${path}"
EOF
done

if [ "${dry_run}" != "1" ]; then
    kubectl apply -f "${bk_esb_custom_components_k8s_yaml}" >&2 || exit 1
fi

cat "${bk_esb_custom_components_values_yaml}"
