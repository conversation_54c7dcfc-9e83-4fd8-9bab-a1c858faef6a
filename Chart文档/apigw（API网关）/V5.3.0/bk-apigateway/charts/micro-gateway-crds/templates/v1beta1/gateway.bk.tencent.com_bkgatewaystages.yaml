{{- if not (.Capabilities.APIVersions.Has "apiextensions.k8s.io/v1") -}}

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.0
    helm.sh/resource-policy: keep
  creationTimestamp: null
  name: bkgatewaystages.gateway.bk.tencent.com
spec:
  group: gateway.bk.tencent.com
  names:
    kind: BkGatewayStage
    listKind: BkGatewayStageList
    plural: bkgatewaystages
    singular: bkgatewaystage
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      description: BkGatewayStage is the Schema for the bkgatewaystages API
      properties:
        apiVersion:
          description: 'APIVersion defines the versioned schema of this representation
            of an object. Servers should convert recognized schemas to the latest
            internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
          type: string
        kind:
          description: 'Kind is a string value representing the REST resource this
            object represents. Servers may infer this from the endpoint the client
            submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
          type: string
        metadata:
          type: object
        spec:
          description: BkGatewayStageSpec defines the desired state of BkGatewayStage
          properties:
            desc:
              description: Desc description for stage
              type: string
            domain:
              description: Domain domain name for stage
              type: string
            name:
              type: string
            pathPrefix:
              description: PathPrefix unified prefix for path
              type: string
            plugins:
              description: Plugins plugins for stage
              items:
                properties:
                  config:
                    description: Config parameter of plugin
                    type: object
                  name:
                    description: Name name of plugin
                    type: string
                type: object
              type: array
            rewrite:
              description: Rewrite rewrite config for stage
              properties:
                enabled:
                  description: Enabled if rewrite is enabled
                  type: boolean
                headers:
                  additionalProperties:
                    type: string
                  description: Headers headers for rewrite
                  type: object
              type: object
            vars:
              additionalProperties:
                type: string
              description: Vars environment vairiables
              type: object
          type: object
        status:
          description: BkGatewayStageStatus defines the observed state of BkGatewayStage
          properties:
            message:
              description: Message message for bk gateway stage
              type: string
            status:
              description: Status status for bk gateway stage
              type: string
          type: object
      type: object
  version: v1beta1
  versions:
  - name: v1beta1
    served: true
    storage: true
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
{{- end }}
