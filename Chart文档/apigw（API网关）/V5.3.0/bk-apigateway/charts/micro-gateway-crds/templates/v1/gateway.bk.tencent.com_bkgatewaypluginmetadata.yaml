{{- if .Capabilities.APIVersions.Has "apiextensions.k8s.io/v1" -}}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.9.2
    helm.sh/resource-policy: keep
  creationTimestamp: null
  name: bkgatewaypluginmetadata.gateway.bk.tencent.com
spec:
  group: gateway.bk.tencent.com
  names:
    kind: BkGatewayPluginMetadata
    listKind: BkGatewayPluginMetadataList
    plural: bkgatewaypluginmetadata
    singular: bkgatewaypluginmetadata
  scope: Namespaced
  versions:
  - name: v1beta1
    schema:
      openAPIV3Schema:
        description: BkGatewayPluginMetadata is the Schema for the bkgatewaypluginmetadatas
          API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: BkGatewayPluginMetadataSpec defines the desired state of
              BkGatewayPluginMetadata
            properties:
              config:
                type: object
                x-kubernetes-preserve-unknown-fields: true
              name:
                type: string
            required:
            - config
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
{{- end }}
