{{- if .Capabilities.APIVersions.Has "apiextensions.k8s.io/v1" -}}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.9.2
    helm.sh/resource-policy: keep
  creationTimestamp: null
  name: bkgatewayoperators.gateway.bk.tencent.com
spec:
  group: gateway.bk.tencent.com
  names:
    kind: BkGatewayOperator
    listKind: BkGatewayOperatorList
    plural: bkgatewayoperators
    singular: bkgatewayoperator
  scope: Namespaced
  versions:
  - name: v1beta1
    schema:
      openAPIV3Schema:
        description: BkGatewayOperator is the Schema for the BkGatewayOperator API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: BkGatewayOperator defines the desired state of BkGatewayOperator
            properties:
              configSchema:
                type: object
              discoveryType:
                type: string
            type: object
          status:
            description: BkGatewayOperatorStatus defines the observed state of BkGatewayOperator
            properties:
              message:
                description: Message message for bk gateway stage
                type: string
              readyUntil:
                description: when operator status is Ready and time pass the ReadyUntil,
                  operator should regard status as NotReady
                format: date-time
                type: string
              status:
                description: Status status for bk gateway stage
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
{{- end }}
