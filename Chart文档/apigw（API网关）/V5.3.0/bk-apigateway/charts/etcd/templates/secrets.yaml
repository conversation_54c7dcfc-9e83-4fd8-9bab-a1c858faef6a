{{- if and (or .Values.auth.rbac.create .Values.auth.rbac.enabled) (not .Values.auth.rbac.existingSecret) -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if .Values.auth.rbac.rootPassword }}
  etcd-root-password: {{ .Values.auth.rbac.rootPassword | b64enc | quote }}
  {{- else }}
  etcd-root-password: {{ randAlphaNum 10 | b64enc | quote }}
  {{- end }}
{{- end }}
