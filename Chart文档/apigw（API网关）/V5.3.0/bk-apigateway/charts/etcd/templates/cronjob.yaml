{{- if .Values.disasterRecovery.enabled -}}
apiVersion: {{ include "common.capabilities.cronjob.apiVersion" . }}
kind: CronJob
metadata:
  name: {{ printf "%s-snapshotter" (include "common.names.fullname" .) | trunc 63 | trimSuffix "-" }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  concurrencyPolicy: Forbid
  schedule: {{ .Values.disasterRecovery.cronjob.schedule | quote }}
  successfulJobsHistoryLimit: {{ .Values.disasterRecovery.cronjob.historyLimit }}
  jobTemplate:
    spec:
      template:
        metadata:
          labels: {{- include "common.labels.standard" . | nindent 12 }}
            app.kubernetes.io/component: snapshotter
          {{- if .Values.disasterRecovery.cronjob.podAnnotations }}
          annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.disasterRecovery.cronjob.podAnnotations "context" $) | nindent 12 }}
          {{- end }}
        spec:
          {{- if .Values.disasterRecovery.cronjob.nodeSelector }}
          nodeSelector: {{-  toYaml .Values.disasterRecovery.cronjob.nodeSelector | nindent 12  }}
          {{- end }}
          {{- if .Values.disasterRecovery.cronjob.tolerations }}
          tolerations: {{- toYaml .Values.disasterRecovery.cronjob.tolerations | nindent 12 }}
          {{- end }}
          {{- include "etcd.imagePullSecrets" . | nindent 10 }}
          restartPolicy: OnFailure
          {{- if .Values.podSecurityContext.enabled }}
          securityContext: {{- omit .Values.podSecurityContext "enabled" | toYaml | nindent 12 }}
          {{- end }}
          {{- if and .Values.volumePermissions.enabled (or .Values.podSecurityContext.enabled .Values.containerSecurityContext.enabled) }}
          initContainers:
            - name: volume-permissions
              image: {{ include "etcd.volumePermissions.image" . }}
              imagePullPolicy: {{ .Values.volumePermissions.image.pullPolicy | quote }}
              command:
                - /bin/bash
                - -ec
                - |
                  chown -R {{ .Values.containerSecurityContext.runAsUser }}:{{ .Values.podSecurityContext.fsGroup }} /snapshots
              securityContext:
                runAsUser: 0
              {{- if .Values.volumePermissions.resources }}
              resources: {{- include "common.tplvalues.render" (dict "value" .Values.volumePermissions.resources "context" $) | nindent 16 }}
              {{- end }}
              volumeMounts:
                - name: snapshot-volume
                  mountPath: /snapshots
          {{- end }}
          containers:
            - name: etcd-snapshotter
              image: {{ include "etcd.image" . }}
              imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
              {{- if .Values.containerSecurityContext.enabled }}
              securityContext: {{- omit .Values.containerSecurityContext "enabled" | toYaml | nindent 16 }}
              {{- end }}
              {{- if .Values.diagnosticMode.enabled }}
              command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 16 }}
              args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 16 }}
              {{- else }}
              command:
                - /opt/bitnami/scripts/etcd/snapshot.sh
              {{- end }}
              env:
                - name: BITNAMI_DEBUG
                  value: {{ ternary "true" "false" (or .Values.image.debug .Values.diagnosticMode.enabled) | quote }}
                - name: ETCDCTL_API
                  value: "3"
                - name: ETCD_ON_K8S
                  value: "yes"
                - name: MY_STS_NAME
                  value: {{ include "common.names.fullname" . | quote }}
                {{- $releaseNamespace := .Release.Namespace }}
                {{- $etcdFullname := include "common.names.fullname" . }}
                {{- $etcdHeadlessServiceName := (printf "%s-%s" $etcdFullname "headless" | trunc 63 | trimSuffix "-") }}
                {{- $clusterDomain := .Values.clusterDomain }}
                - name: ETCD_CLUSTER_DOMAIN
                  value: {{ printf "%s.%s.svc.%s" $etcdHeadlessServiceName $releaseNamespace $clusterDomain | quote }}
                - name: ETCD_SNAPSHOT_HISTORY_LIMIT
                  value: {{ .Values.disasterRecovery.cronjob.snapshotHistoryLimit | quote }}
                {{- if .Values.auth.client.secureTransport }}
                - name: ETCD_CERT_FILE
                  value: "/opt/bitnami/etcd/certs/client/{{ .Values.auth.client.certFilename }}"
                - name: ETCD_KEY_FILE
                  value: "/opt/bitnami/etcd/certs/client/{{ .Values.auth.client.certKeyFilename }}"
                {{- if .Values.auth.client.enableAuthentication }}
                - name: ETCD_CLIENT_CERT_AUTH
                  value: "true"
                - name: ETCD_TRUSTED_CA_FILE
                  value: "/opt/bitnami/etcd/certs/client/{{ .Values.auth.client.caFilename | default "ca.crt" }}"
                {{- else if .Values.auth.client.caFilename }}
                - name: ETCD_TRUSTED_CA_FILE
                  value: "/opt/bitnami/etcd/certs/client/{{ .Values.auth.client.caFilename | default "ca.crt" }}"
                {{- end }}
                {{- end }}
                {{- if or .Values.auth.rbac.create .Values.auth.rbac.enabled }}
                - name: ETCD_ROOT_PASSWORD
                  valueFrom:
                    secretKeyRef:
                      name: {{ include "etcd.secretName" . }}
                      key: {{ include "etcd.secretPasswordKey" . }}
                {{- end }}
              {{- if .Values.disasterRecovery.cronjob.resources }}
              resources: {{- toYaml .Values.disasterRecovery.cronjob.resources | nindent 16 }}
              {{- end }}
              volumeMounts:
                - name: snapshot-volume
                  mountPath: /snapshots
                {{- if .Values.auth.client.secureTransport }}
                - name: certs
                  mountPath: /opt/bitnami/etcd/certs/client
                  readOnly: true
                {{- end }}
          volumes:
            {{- if .Values.auth.client.secureTransport }}
            - name: certs
              secret:
                secretName: {{ required "A secret containinig the client certificates is required" (tpl .Values.auth.client.existingSecret .) }}
                defaultMode: 256
            {{- end }}
            - name: snapshot-volume
              persistentVolumeClaim:
                claimName: {{ include "etcd.disasterRecovery.pvc.name" . }}
{{- end }}
