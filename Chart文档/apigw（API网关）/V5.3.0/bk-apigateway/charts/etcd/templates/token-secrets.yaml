{{- if (include "etcd.token.createSecret" .) }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-jwt-token" (include "common.names.fullname" .) | trunc 63 | trimSuffix "-" }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  jwt-token.pem: {{ include "etcd.token.jwtToken" . | b64enc | quote }}
{{- end }}
