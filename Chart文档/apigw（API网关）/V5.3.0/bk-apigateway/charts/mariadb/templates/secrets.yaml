{{- if eq (include "mariadb.createSecret" .) "true" }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ include "common.names.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  {{- if (not .Values.auth.forcePassword) }}
  mariadb-root-password: {{ include "common.secrets.passwords.manage" (dict "secret" (include "common.names.fullname" .) "key" "mariadb-root-password" "providedValues" (list "auth.rootPassword") "context" $) }}
  {{- else }}
  mariadb-root-password: {{ required "A MariaDB Root Password is required!" .Values.auth.rootPassword | b64enc | quote }}
  {{- end }}
  {{- if (not (empty .Values.auth.username)) }}
  {{- if (not .Values.auth.forcePassword) }}
  mariadb-password: {{ include "common.secrets.passwords.manage" (dict "secret" (include "common.names.fullname" .) "key" "mariadb-password" "providedValues" (list "auth.password") "context" $) }}
  {{- else }}
  mariadb-password: {{ required "A MariaDB Database Password is required!" .Values.auth.password | b64enc | quote }}
  {{- end }}
  {{- end }}
  {{- if eq .Values.architecture "replication" }}
  {{- if (not .Values.auth.forcePassword) }}
  mariadb-replication-password: {{ include "common.secrets.passwords.manage" (dict "secret" (include "common.names.fullname" .) "key" "mariadb-replication-password" "providedValues" (list "auth.replicationPassword") "context" $) }}
  {{- else }}
  mariadb-replication-password: {{ required "A MariaDB Replication Password is required!" .Values.auth.replicationPassword | b64enc | quote }}
  {{- end }}
  {{- end }}
{{- end }}
