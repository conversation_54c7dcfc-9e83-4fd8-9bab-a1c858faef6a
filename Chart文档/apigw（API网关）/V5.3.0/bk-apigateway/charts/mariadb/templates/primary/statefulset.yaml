apiVersion: {{ include "common.capabilities.statefulset.apiVersion" . }}
kind: StatefulSet
metadata:
  name: {{ include "mariadb.primary.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    app.kubernetes.io/component: primary
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: 1
  revisionHistoryLimit: {{ .Values.primary.revisionHistoryLimit }}
  selector:
    matchLabels: {{ include "common.labels.matchLabels" . | nindent 6 }}
      app.kubernetes.io/component: primary
  serviceName: {{ include "mariadb.primary.fullname" . }}
  {{- if .Values.primary.updateStrategy }}
  updateStrategy: {{- toYaml .Values.primary.updateStrategy | nindent 4 }}
  {{- end }}
  template:
    metadata:
      annotations:
        {{- if (include "mariadb.primary.createConfigmap" .) }}
        checksum/configuration: {{ include (print $.Template.BasePath "/primary/configmap.yaml") . | sha256sum }}
        {{- end }}
        {{- if .Values.primary.podAnnotations }}
        {{- include "common.tplvalues.render" (dict "value" .Values.primary.podAnnotations "context" $) | nindent 8 }}
        {{- end }}
      labels: {{- include "common.labels.standard" . | nindent 8 }}
        app.kubernetes.io/component: primary
        {{- if .Values.primary.podLabels }}
        {{- include "common.tplvalues.render" (dict "value" .Values.primary.podLabels "context" $) | nindent 8 }}
        {{- end }}
        {{- if .Values.commonLabels }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 8 }}
        {{- end }}
    spec:
      {{- include "mariadb.imagePullSecrets" . | nindent 6 }}
      {{- if .Values.primary.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.primary.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if or .Values.primary.schedulerName .Values.schedulerName }}
      schedulerName: {{ (coalesce .Values.primary.schedulerName .Values.schedulerName) | quote }}
      {{- end }}
      serviceAccountName: {{ template "mariadb.serviceAccountName" . }}
      {{- if .Values.primary.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.primary.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.primary.podAffinityPreset "component" "primary" "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.primary.podAntiAffinityPreset "component" "primary" "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.primary.nodeAffinityPreset.type "key" .Values.primary.nodeAffinityPreset.key "values" .Values.primary.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.primary.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.primary.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.primary.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.primary.tolerations "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.primary.schedulerName }}
      schedulerName: {{ .Values.primary.schedulerName }}
      {{- end }}
      {{- if .Values.primary.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.primary.topologySpreadConstraints "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.primary.priorityClassName }}
      priorityClassName: {{ .Values.primary.priorityClassName | quote }}
      {{- else if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName | quote }}
      {{- end }}
      {{- if .Values.primary.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.primary.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      {{- if or .Values.primary.initContainers (and .Values.primary.podSecurityContext.enabled .Values.volumePermissions.enabled .Values.primary.persistence.enabled) }}
      initContainers:
        {{- if .Values.primary.initContainers }}
        {{- include "common.tplvalues.render" (dict "value" .Values.primary.initContainers "context" $) | nindent 8 }}
        {{- end }}
        {{- if and .Values.primary.podSecurityContext.enabled .Values.volumePermissions.enabled .Values.primary.persistence.enabled }}
        - name: volume-permissions
          image: {{ include "mariadb.volumePermissions.image" . }}
          imagePullPolicy: {{ .Values.volumePermissions.image.pullPolicy | quote }}
          command:
            - /bin/bash
            - -ec
            - |
              chown -R {{ .Values.primary.containerSecurityContext.runAsUser }}:{{ .Values.primary.podSecurityContext.fsGroup }} /bitnami/mariadb
          securityContext:
            runAsUser: 0
          {{- if .Values.volumePermissions.resources }}
          resources: {{- toYaml .Values.volumePermissions.resources | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: data
              mountPath: /bitnami/mariadb
              {{- if .Values.primary.persistence.subPath }}
              subPath: {{ .Values.primary.persistence.subPath }}
              {{- end }}
        {{- end }}
      {{- end }}
      containers:
        - name: mariadb
          image: {{ include "mariadb.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          {{- if .Values.primary.containerSecurityContext.enabled }}
          securityContext: {{- omit .Values.primary.containerSecurityContext "enabled" | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.primary.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.primary.command "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.primary.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.primary.args "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.primary.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.primary.lifecycleHooks "context" $) | nindent 12 }}
          {{- end }}
          env:
            - name: BITNAMI_DEBUG
              value: {{ ternary "true" "false" (or .Values.image.debug .Values.diagnosticMode.enabled) | quote }}
            {{- if .Values.auth.usePasswordFiles }}
            - name: MARIADB_ROOT_PASSWORD_FILE
              value: {{ default "/opt/bitnami/mariadb/secrets/mariadb-root-password" .Values.auth.customPasswordFiles.root }}
            {{- else }}
            - name: MARIADB_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "mariadb.secretName" . }}
                  key: mariadb-root-password
            {{- end }}
            {{- if not (empty .Values.auth.username) }}
            - name: MARIADB_USER
              value: {{ .Values.auth.username | quote }}
            {{- if .Values.auth.usePasswordFiles }}
            - name: MARIADB_PASSWORD_FILE
              value: {{ default "/opt/bitnami/mariadb/secrets/mariadb-password" .Values.auth.customPasswordFiles.user }}
            {{- else }}
            - name: MARIADB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "mariadb.secretName" . }}
                  key: mariadb-password
            {{- end }}
            {{- end }}
            - name: MARIADB_DATABASE
              value: {{ .Values.auth.database | quote }}
            {{- if eq .Values.architecture "replication" }}
            - name: MARIADB_REPLICATION_MODE
              value: "master"
            - name: MARIADB_REPLICATION_USER
              value: {{ .Values.auth.replicationUser | quote }}
            {{- if .Values.auth.usePasswordFiles }}
            - name: MARIADB_REPLICATION_PASSWORD_FILE
              value: {{ default "/opt/bitnami/mariadb/secrets/mariadb-replication-password" .Values.auth.customPasswordFiles.replicator }}
            {{- else }}
            - name: MARIADB_REPLICATION_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "mariadb.secretName" . }}
                  key: mariadb-replication-password
            {{- end }}
            {{- end }}
            {{- if .Values.primary.extraFlags }}
            - name: MARIADB_EXTRA_FLAGS
              value: "{{ .Values.primary.extraFlags }}"
            {{- end }}
            {{- if .Values.primary.startupWaitOptions }}
            - name: MARIADB_STARTUP_WAIT_RETRIES
              value: "{{ .Values.primary.startupWaitOptions.retries | default 300 }}"
            - name: MARIADB_STARTUP_WAIT_SLEEP_TIME
              value: "{{ .Values.primary.startupWaitOptions.sleepTime | default 2 }}"
            {{- end }}
            {{- if .Values.primary.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.primary.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          {{- if or .Values.primary.extraEnvVarsCM .Values.primary.extraEnvVarsSecret }}
          envFrom:
            {{- if .Values.primary.extraEnvVarsCM }}
            - configMapRef:
                name: {{ .Values.primary.extraEnvVarsCM }}
            {{- end }}
            {{- if .Values.primary.extraEnvVarsSecret }}
            - secretRef:
                name: {{ .Values.primary.extraEnvVarsSecret }}
            {{- end }}
          {{- end }}
          ports:
            - name: mysql
              containerPort: 3306
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.primary.startupProbe.enabled }}
          startupProbe: {{- omit .Values.primary.startupProbe "enabled" | toYaml | nindent 12 }}
            exec:
              command:
                - /bin/bash
                - -ec
                - |
                  password_aux="${MARIADB_ROOT_PASSWORD:-}"
                  if [[ -f "${MARIADB_ROOT_PASSWORD_FILE:-}" ]]; then
                      password_aux=$(cat "$MARIADB_ROOT_PASSWORD_FILE")
                  fi
                  mysqladmin status -uroot -p"${password_aux}"
          {{- else if .Values.primary.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.primary.customStartupProbe "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.primary.livenessProbe.enabled }}
          livenessProbe: {{- omit .Values.primary.livenessProbe "enabled" | toYaml | nindent 12 }}
            exec:
              command:
                - /bin/bash
                - -ec
                - |
                  password_aux="${MARIADB_ROOT_PASSWORD:-}"
                  if [[ -f "${MARIADB_ROOT_PASSWORD_FILE:-}" ]]; then
                      password_aux=$(cat "$MARIADB_ROOT_PASSWORD_FILE")
                  fi
                  mysqladmin status -uroot -p"${password_aux}"
          {{- else if .Values.primary.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.primary.customLivenessProbe "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.primary.readinessProbe.enabled }}
          readinessProbe: {{- omit .Values.primary.readinessProbe "enabled" | toYaml | nindent 12 }}
            exec:
              command:
                - /bin/bash
                - -ec
                - |
                  password_aux="${MARIADB_ROOT_PASSWORD:-}"
                  if [[ -f "${MARIADB_ROOT_PASSWORD_FILE:-}" ]]; then
                      password_aux=$(cat "$MARIADB_ROOT_PASSWORD_FILE")
                  fi
                  mysqladmin status -uroot -p"${password_aux}"
          {{- else if .Values.primary.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.primary.customReadinessProbe "context" $) | nindent 12 }}
          {{- end }}
          {{- end }}
          {{- if .Values.primary.resources }}
          resources: {{ toYaml .Values.primary.resources | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: data
              mountPath: /bitnami/mariadb
              {{- if .Values.primary.persistence.subPath }}
              subPath: {{ .Values.primary.persistence.subPath }}
              {{- end }}
            {{- if or .Values.initdbScriptsConfigMap .Values.initdbScripts }}
            - name: custom-init-scripts
              mountPath: /docker-entrypoint-initdb.d
            {{- end }}
            {{- if or .Values.primary.configuration .Values.primary.existingConfigmap }}
            - name: config
              mountPath: /opt/bitnami/mariadb/conf/my.cnf
              subPath: my.cnf
            {{- end }}
            {{- if and .Values.auth.usePasswordFiles (not .Values.auth.customPasswordFiles) }}
            - name: mariadb-credentials
              mountPath: /opt/bitnami/mariadb/secrets/
            {{- end }}
            {{- if .Values.primary.extraVolumeMounts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.primary.extraVolumeMounts "context" $) | nindent 12 }}
            {{- end }}
        {{- if .Values.metrics.enabled }}
        - name: metrics
          image: {{ include "mariadb.metrics.image" . }}
          imagePullPolicy: {{ .Values.metrics.image.pullPolicy | quote }}
          {{- if .Values.metrics.containerSecurityContext.enabled }}
          securityContext: {{- omit .Values.metrics.containerSecurityContext "enabled" | toYaml | nindent 12 }}
          {{- end }}
          env:
            {{- if .Values.auth.usePasswordFiles }}
            - name: MARIADB_ROOT_PASSWORD_FILE
              value: {{ default "/opt/bitnami/mysqld-exporter/secrets/mariadb-root-password" .Values.auth.customPasswordFiles.root }}
            {{- else }}
            - name: MARIADB_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ template "mariadb.secretName" . }}
                  key: mariadb-root-password
            {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else }}
          command:
            - /bin/bash
            - -ec
            - |
              password_aux="${MARIADB_ROOT_PASSWORD:-}"
              if [[ -f "${MARIADB_ROOT_PASSWORD_FILE:-}" ]]; then
                  password_aux=$(cat "$MARIADB_ROOT_PASSWORD_FILE")
              fi
              DATA_SOURCE_NAME="root:${password_aux}@(localhost:3306)/" /bin/mysqld_exporter {{- range .Values.metrics.extraArgs.primary }} {{ . }} {{- end }}
          {{- end }}
          ports:
            - name: metrics
              containerPort: 9104
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.metrics.livenessProbe.enabled }}
          livenessProbe: {{- omit .Values.metrics.livenessProbe "enabled" | toYaml | nindent 12 }}
            httpGet:
              path: /metrics
              port: metrics
          {{- end }}
          {{- if .Values.metrics.readinessProbe.enabled }}
          readinessProbe: {{- omit .Values.metrics.readinessProbe "enabled" | toYaml | nindent 12 }}
            httpGet:
              path: /metrics
              port: metrics
          {{- end }}
          {{- end }}
          {{- if .Values.metrics.resources }}
          resources: {{- toYaml .Values.metrics.resources | nindent 12 }}
          {{- end }}
          {{- if and .Values.auth.usePasswordFiles (not .Values.auth.customPasswordFiles) }}
          volumeMounts:
            - name: mariadb-credentials
              mountPath: /opt/bitnami/mysqld-exporter/secrets/
          {{- end }}
        {{- end }}
        {{- if .Values.primary.sidecars }}
        {{- include "common.tplvalues.render" (dict "value" .Values.primary.sidecars "context" $) | nindent 8 }}
        {{- end }}
      volumes:
        {{- if or .Values.primary.configuration .Values.primary.existingConfigmap }}
        - name: config
          configMap:
            name: {{ include "mariadb.primary.configmapName" . }}
        {{- end }}
        {{- if or .Values.initdbScriptsConfigMap .Values.initdbScripts }}
        - name: custom-init-scripts
          configMap:
            name: {{ template "mariadb.initdbScriptsCM" . }}
        {{- end }}
        {{- if and .Values.auth.usePasswordFiles (not .Values.auth.customPasswordFiles) }}
        - name: mariadb-credentials
          secret:
            secretName: {{ template "mariadb.secretName" . }}
            items:
              - key: mariadb-root-password
                path: mariadb-root-password
              - key: mariadb-password
                path: mariadb-password
              {{- if eq .Values.architecture "replication" }}
              - key: mariadb-replication-password
                path: mariadb-replication-password
              {{- end }}
        {{- end }}
        {{- if .Values.primary.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.primary.extraVolumes "context" $) | nindent 8 }}
        {{- end }}
  {{- if and .Values.primary.persistence.enabled .Values.primary.persistence.existingClaim }}
        - name: data
          persistentVolumeClaim:
            claimName: {{ tpl .Values.primary.persistence.existingClaim . }}
  {{- else if not .Values.primary.persistence.enabled }}
        - name: data
          emptyDir: {}
  {{- else if and .Values.primary.persistence.enabled (not .Values.primary.persistence.existingClaim) }}
  volumeClaimTemplates:
    - metadata:
        name: data
        labels: {{ include "common.labels.matchLabels" . | nindent 10 }}
          app.kubernetes.io/component: primary
        {{- if .Values.primary.persistence.annotations }}
        annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.primary.persistence.annotations "context" $ ) | nindent 10 }}
        {{- end }}
      spec:
        accessModes:
          {{- range .Values.primary.persistence.accessModes }}
          - {{ . | quote }}
          {{- end }}
        resources:
          requests:
            storage: {{ .Values.primary.persistence.size | quote }}
        {{ include "common.storage.class" (dict "persistence" .Values.primary.persistence "global" .Values.global) }}
        {{- if .Values.primary.persistence.selector }}
        selector: {{- include "common.tplvalues.render" (dict "value" .Values.primary.persistence.selector "context" $) | nindent 10 }}
        {{- end -}}
  {{- end }}
