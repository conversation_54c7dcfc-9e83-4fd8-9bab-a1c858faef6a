{{- if and .Values.auth.enabled (not .Values.auth.existingSecret) -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "common.names.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if or .Values.secretAnnotations .Values.commonAnnotations }}
  annotations:
      {{- if .Values.secretAnnotations }}
      {{- include "common.tplvalues.render" ( dict "value" .Values.secretAnnotations "context" $ ) | nindent 4 }}
      {{- end }}
      {{- if .Values.commonAnnotations }}
      {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
      {{- end }}
  {{- end }}
type: Opaque
data:
  redis-password: {{ include "redis.password" . | b64enc | quote }}
{{- end -}}
