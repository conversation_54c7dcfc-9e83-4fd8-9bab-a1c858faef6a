apiVersion: v2
appVersion: 1.12.14
dependencies:
- condition: registerCrds
  name: micro-gateway-crds
  repository: file://./charts/micro-gateway-crds
  version: x.x.x
- name: common
  repository: https://charts.bitnami.com/bitnami
  version: 1.x.x
- condition: mariadb.enabled
  name: mariadb
  repository: https://charts.bitnami.com/bitnami
  version: 11.0.x
- condition: redis.enabled
  name: redis
  repository: https://charts.bitnami.com/bitnami
  version: 16.12.x
- condition: etcd.enabled
  name: etcd
  repository: https://charts.bitnami.com/bitnami
  version: 8.8.x
description: A full stack chart for Apigateway Enterprise products.
name: bk-apigateway
type: application
version: 1.12.14
