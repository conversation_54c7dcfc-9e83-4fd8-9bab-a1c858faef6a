{{- if .Values.bkEsb.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "apigw-helm-stacks.job-name" (list . "bk-esb-migrate") }}
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  backoffLimit: 10
  parallelism: 1
  template:
    metadata:
      annotations: {{ include "apigw-helm-stacks.pod-annotations" . | nindent 8 }}
      labels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 8 }}
        app.kubernetes.io/component: "bk-esb-migrate"
    spec:
      serviceAccountName: {{ include "apigw-helm-stacks.service-account-name" . }}
      affinity: {{ include "apigw-helm-stacks.affinity" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      tolerations: {{ include "apigw-helm-stacks.tolerations" . | nindent 8 }}
      hostAliases: {{ include "apigw-helm-stacks.host-aliases" . | nindent 8 }}
      initContainers:
        {{- include "apigw-helm-stacks.wait-for-migrations-init-container" . | nindent 8 }}
      containers:
      - command:
        - bash
        args:
        - /app/bin/on_migrate
        env: {{ include "apigw-helm-stacks.yaml" .Values.bkEsb.extraEnvVars | nindent 10 }}
        envFrom:
        - configMapRef:
            name: {{ include "apigw-helm-stacks.name-prefix" . }}-basic-env
        - secretRef:
            name: {{ include "apigw-helm-stacks.name-prefix" . }}-encrypt-key
        image: {{ include "apigw-helm-stacks.image" (list  .Values.bkEsb.image .Values.global) }}
        imagePullPolicy: {{ include "apigw-helm-stacks.image-pull-policy" .Values.bkEsb.image }}
        name: bk-esb-migrate
        resources: {{ include "apigw-helm-stacks.container-resources" .Values.bkEsb | nindent 10 }}
        volumeMounts: {{- include "apigw-helm-stacks.bk-esb.volume-mounts" . | nindent 10 }}
      imagePullSecrets: {{ include "apigw-helm-stacks.image-pull-secrets" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      restartPolicy: Never
      securityContext: {{ include "apigw-helm-stacks.container-security-context" . | nindent 8 }}
      volumes: {{- include "apigw-helm-stacks.bk-esb.volumes" . | nindent 8 }}
  ttlSecondsAfterFinished: 86400
{{- end }}