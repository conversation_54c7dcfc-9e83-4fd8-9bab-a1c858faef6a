{{/*
Pod volumes
*/}}
{{- define "apigw-helm-stacks.bk-esb.volumes" -}}
- name: certs
  secret:
    secretName: {{ include "apigw-helm-stacks.name-prefix" . }}-bk-esb-certs
- name: confpy
  configMap:
    name: {{ include "apigw-helm-stacks.name-prefix" . }}-bk-esb-confpy

{{- if .Values.bkEsb.customComponentConfigMaps }}
{{- range $item := .Values.bkEsb.customComponentConfigMaps }}
- name: {{ $item.configmap | replace "." "-" }}
  configMap:
    name: {{ $item.configmap }}
{{- end }}
{{- end }}
{{ include "apigw-helm-stacks.yaml" .Values.bkEsb.extraVolumes }}
{{- end }}

{{/*
Pod volume mounts
*/}}
{{- define "apigw-helm-stacks.bk-esb.volume-mounts" -}}
- name: certs
  mountPath: /cert

{{- if .Values.bkEsb.customComponentConfigMaps }}
{{- range $item := .Values.bkEsb.customComponentConfigMaps }}
- name: {{ $item.configmap | replace "." "-" }}
  mountPath: "/app/components/generic/apis/{{ $item.mountPath | required "mountPath should not be empty" }}"
  {{- if $item.subPath }}
  subPath: "{{ $item.subPath }}"
  {{- end }}
{{- end }}
{{- end }}
{{ include "apigw-helm-stacks.yaml" .Values.bkEsb.extraVolumeMounts }}
{{- end }}
