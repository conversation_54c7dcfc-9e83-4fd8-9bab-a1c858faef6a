{{- if and .Values.bkEsb.enabled .Values.bkEsb.bkLogConfig.enabled }}
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-bk-esb-stdout
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  dataId: {{ .Values.bkEsb.bkLogConfig.stdoutDataId }}
  encoding: {{ .Values.bkEsb.bkLogConfig.stdoutEncoding }}
  logConfigType: std_log_config
  namespace: {{ .Release.Namespace }}
  labelSelector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "bk-esb"
---
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-bk-esb-container
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  dataId: {{ .Values.bkEsb.bkLogConfig.containerDataId }}
  encoding: {{ .Values.bkEsb.bkLogConfig.containerEncoding }}
  logConfigType: container_log_config
  namespace: {{ .Release.Namespace }}
  labelSelector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "bk-esb"
  path:
    - {{ .Values.bkEsb.bkLogConfig.logPath | trimSuffix "/" }}/esb_api.log
{{- end }}
