{{- if .Values.bkEsb.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-bk-esb
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
    app.kubernetes.io/component: "bk-esb"
spec:
  type: {{ .Values.bkEsb.service.type }}
  selector: {{ include "apigw-helm-stacks.service-selector-labels" . | nindent 4 }}
    app.kubernetes.io/component: "bk-esb"
  ports:
    - name: http
      port: {{ .Values.bkEsb.service.httpPort }}
      protocol: TCP
      targetPort: http
{{- end }}