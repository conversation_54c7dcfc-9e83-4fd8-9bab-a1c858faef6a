{{/*
Expand the name of the chart.
*/}}
{{- define "apigw-helm-stacks.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "apigw-helm-stacks.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create a default name prefix for the resources which truncate at 32 chars.
*/}}
{{- define "apigw-helm-stacks.name-prefix" -}}
{{- include "apigw-helm-stacks.fullname" . | trunc 32 | trimSuffix "-" -}}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "apigw-helm-stacks.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "apigw-helm-stacks.labels" -}}
app.kubernetes.io/name: {{ include "apigw-helm-stacks.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
helm.sh/chart: {{ include "apigw-helm-stacks.chart" . }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "apigw-helm-stacks.selector-labels" -}}
app.kubernetes.io/name: {{ include "apigw-helm-stacks.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Service selector labels
*/}}
{{- define "apigw-helm-stacks.service-selector-labels" -}}
app.kubernetes.io/name: {{ include "apigw-helm-stacks.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "apigw-helm-stacks.service-account-name" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "apigw-helm-stacks.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Render the host aliases
*/}}
{{- define "apigw-helm-stacks.host-aliases" -}}
{{- if .Values.global.hostAliases }}
{{- include "common.tplvalues.render" (dict "value" .Values.global.hostAliases "context" $) }}
{{- end }}
{{- end }}

{{/*
Render the image
*/}}
{{- define "apigw-helm-stacks.image" -}}
{{- $image := first . -}}
{{- $global := last . -}}
{{- include "common.images.image" (dict "imageRoot" $image "global" $global) }}
{{- end }}

{{/*
Construct a full URL by combining the base url and path list
*/}}
{{- define "apigw-helm-stacks.urljoin" -}}
{{- $parts := list -}}

{{- range $part := . -}}
{{- $parts = append $parts ($part | trimSuffix "/" | trimPrefix "/") -}}
{{- end -}}

{{- join "/" $parts -}}
{{- end -}}

{{/*
Render the ingress host
*/}}
{{- define "apigw-helm-stacks.ingress-host" -}}
{{- $ingress := first . -}}
{{- $global := last . -}}
{{- $ingress.name -}}.{{- $global.bkDomain -}}
{{- end }}

{{/*
Render the ingress url
*/}}
{{- define "apigw-helm-stacks.ingress-url" -}}
{{- $ingress := first . -}}
{{- $global := last . -}}
{{- $host := include "apigw-helm-stacks.ingress-host" (list $ingress $global) -}}
{{- if $ingress.port -}}
{{- $host = printf "%s:%v" $host $ingress.port -}}
{{- end -}}
{{ $global.bkDomainScheme }}://{{ include "apigw-helm-stacks.urljoin" (list $host $ingress.path) | trimSuffix "/" }}
{{- end }}


{{/*
Make a ingress url + custom path
*/}}
{{- define "apigw-helm-stacks.dashboard-ingress-url-root-path" -}}
{{- $ingress := first . -}}
{{- $global := last . -}}
{{- $host := include "apigw-helm-stacks.ingress-host" (list $ingress $global) -}}
{{- if $ingress.port -}}
{{- $host = printf "%s:%v" $host $ingress.port -}}
{{- end -}}
{{ $global.bkDomainScheme }}://{{ include "apigw-helm-stacks.urljoin" (list $host "") | trimSuffix "/" }}
{{- end }}

{{/*
Render the ingress class
*/}}
{{- define "apigw-helm-stacks.ingress-class" -}}
{{- if .Values.ingressClassName -}}
ingressClassName: {{ .Values.ingressClassName }}
{{- end -}}
{{- end }}

{{/*
Image pull secrets
*/}}
{{- define "apigw-helm-stacks.image-pull-secrets" -}}
{{- .Values.global.imagePullSecrets | default list | toYaml -}}
{{- end }}

{{/*
pod affinity
*/}}
{{- define "apigw-helm-stacks.affinity" -}}
{{- .Values.affinity | default dict | toYaml -}}
{{- end }}

{{/*
pod node selector
*/}}
{{- define "apigw-helm-stacks.node-selector" -}}
{{- .Values.nodeSelector | default dict | toYaml -}}
{{- end }}

{{/*
pod tolerations
*/}}
{{- define "apigw-helm-stacks.tolerations" -}}
{{- .Values.tolerations | default list | toYaml -}}
{{- end }}

{{/*
pod annotations
*/}}
{{- define "apigw-helm-stacks.pod-annotations" -}}
{{- .Values.podAnnotations | default dict | toYaml -}}
{{- end }}

{{/*
image pull policy
*/}}
{{- define "apigw-helm-stacks.image-pull-policy" -}}
{{- .pullPolicy | default "IfNotPresent" -}}
{{- end }}

{{/*
deployment replica count
*/}}
{{- define "apigw-helm-stacks.deployment-replicas" -}}
{{- $process := first . -}}
{{- $values := last . -}}
{{- ternary $values.replicaCount $process.replicaCount (typeIs "<nil>" $process.replicaCount) -}}
{{- end }}

{{/*
deployment resources
*/}}
{{- define "apigw-helm-stacks.container-resources" -}}
{{ toYaml .resources }}
{{- end }}


{{/*
dump to yaml optionally
*/}}
{{- define "apigw-helm-stacks.yaml" -}}
{{- with . -}}
{{- toYaml . -}}
{{- end -}}
{{- end }}

{{/*
deployment security context
*/}}
{{- define "apigw-helm-stacks.container-security-context" -}}
{{ .Values.securityContext | default dict | toYaml -}}
{{- end }}

{{/*
Create a job name with default prefix and release revision.
*/}}
{{- define "apigw-helm-stacks.job-name" -}}
{{- $root := first . -}}
{{- $name := last . -}}
{{- include "apigw-helm-stacks.name-prefix" $root }}-{{ $name }}-{{ $root.Release.Revision }}
{{- end }}


{{/*
Wait for job, at least one pod in that job to have 'Succeeded' state, does not mind some 'Failed' ones
*/}}
{{- define "apigw-helm-stacks.wait-for-job-init-container" -}}
{{- $root := first . -}}
{{- $name := last . -}}
- name: {{ $name }}
  image: {{ include "apigw-helm-stacks.image" (list  $root.Values.k8sWaitFor.image $root.Values.global) }}
  imagePullPolicy: {{ $root.Values.k8sWaitFor.image.pullPolicy }}
  resources: {{ toYaml $root.Values.k8sWaitFor.resources | nindent 4 }}
  args:
    - job-wr
    - {{ include "apigw-helm-stacks.job-name" (list $root $name) }}
{{- end }}

{{/*
Create a job name with default prefix and without release revision.
*/}}
{{- define "apigw-helm-stacks.static-job-name" -}}
{{- $root := first . -}}
{{- $name := last . -}}
{{- include "apigw-helm-stacks.name-prefix" $root }}-{{ $name }}
{{- end }}

{{/*
Wait for job (jos name is static), at least one pod in that job to have 'Succeeded' state, does not mind some 'Failed' ones
*/}}
{{- define "apigw-helm-stacks.wait-for-static-job-init-container" -}}
{{- $root := first . -}}
{{- $name := last . -}}
- name: {{ $name }}
  image: {{ include "apigw-helm-stacks.image" (list  $root.Values.k8sWaitFor.image $root.Values.global) }}
  imagePullPolicy: {{ $root.Values.k8sWaitFor.image.pullPolicy }}
  resources: {{ toYaml $root.Values.k8sWaitFor.resources | nindent 4 }}
  args:
    - job-wr
    - {{ include "apigw-helm-stacks.static-job-name" (list $root $name) }}
{{- end }}

{{/*
Wait for pod
*/}}
{{- define "apigw-helm-stacks.wait-for-pod-init-container" -}}
{{- $root := first . -}}
{{- $name := last . -}}
- name: {{ $name }}
  image: {{ include "apigw-helm-stacks.image" (list  $root.Values.k8sWaitFor.image $root.Values.global) }}
  imagePullPolicy: {{ $root.Values.k8sWaitFor.image.pullPolicy }}
  resources: {{ toYaml $root.Values.k8sWaitFor.resources | nindent 4 }}
  args:
    - pod
    - {{ $name }}
{{- end }}

{{/*
Wait for service
*/}}
{{- define "apigw-helm-stacks.wait-for-service-init-container" -}}
{{- $root := first . -}}
{{- $name := last . -}}
- name: {{ $name }}
  image: {{ include "apigw-helm-stacks.image" (list  $root.Values.k8sWaitFor.image $root.Values.global) }}
  imagePullPolicy: {{ $root.Values.k8sWaitFor.image.pullPolicy }}
  resources: {{ toYaml $root.Values.k8sWaitFor.resources | nindent 4 }}
  args:
    - service
    - {{ $name }}
{{- end }}

{{/*
Deployment init containers
*/}}
{{- define "apigw-helm-stacks.wait-for-migrations-init-container" -}}
{{ include "apigw-helm-stacks.wait-for-job-init-container" (list . "wait-migrations") }}
{{- end }}

{{/*
Migrate job init containers
*/}}
{{- define "apigw-helm-stacks.wait-for-storages-job-init-container" -}}
{{ include "apigw-helm-stacks.wait-for-static-job-init-container" (list . "wait-storages") }}
{{- end }}

{{/*
default micro-gateway instance id
*/}}
{{- define "apigw-helm-stacks.micro-gateway.instance-id" -}}
{{- .Values.defaultMicroGatewayInstanceId | default "faf44a48-59e9-f790-2412-e56c90551fb3" -}}
{{- end }}

{{/*
default micro-gateway instance name
*/}}
{{- define "apigw-helm-stacks.micro-gateway.instance-name" -}}
default
{{- end }}

{{/*
default micro-gateway api name
*/}}
{{- define "apigw-helm-stacks.micro-gateway.api-name" -}}
bk-default
{{- end }}

{{/*
default micro-gateway stage name
*/}}
{{- define "apigw-helm-stacks.micro-gateway.stage-name" -}}
default
{{- end }}

{{/*
dashboard service url
*/}}
{{- define "apigw-helm-stacks.dashboard-service-url" -}}
{{- .Values.bkApigwDashboardApiUrl | default (printf "http://%s-dashboard:%v" (include "apigw-helm-stacks.name-prefix" .) .Values.dashboard.service.httpPort) -}}
{{- end -}}

{{/*
bkmonitor provides http host and port, apisix plugin opentelemetry only need address=host:port,
its OTLP HTTP Receiver
*/}}
{{- define "apigw-helm-stacks.trace.address" -}}
"{{ .Values.global.trace.otlp.host }}:{{ .Values.global.trace.otlp.port }}"
{{- end -}}

{{/* bkmonitor provides http host and port, create otlp http url */}}
{{- define "apigw-helm-stacks.trace.http-url" -}}
{{- printf "http://%s:%v/v1/traces" .Values.global.trace.otlp.host .Values.global.trace.otlp.port -}}
{{- end -}}
