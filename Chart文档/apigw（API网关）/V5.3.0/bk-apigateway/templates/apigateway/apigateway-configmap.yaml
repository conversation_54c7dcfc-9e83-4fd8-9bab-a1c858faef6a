{{- if .Values.apigateway.enabled }}
{{- $apisixRedis := fromYaml (include "apigw-helm-stacks.redis.apisix" .) -}}
{{- $apisixEtcd := fromYaml (include "apigw-helm-stacks.etcd.apisix" .) -}}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-apisix-config
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
data:
  config.yaml: |
    bk_gateway:
      bkauth:
        authorization_keys:{{ toYaml .Values.apigateway.apisixConfig.bkAuthAuthorizationKeys | nindent 10 }}
        sensitive_keys:{{ toYaml .Values.apigateway.apisixConfig.bkAuthSensitiveKeys | nindent 10 }}

      bkapp:
        bk_app_code: "{{ .Values.keys.apigatewayAppCode }}"
        bk_app_secret: "{{ .Values.keys.apigatewayAppSecret }}"

      instance:
        id: "{{ include "apigw-helm-stacks.micro-gateway.instance-id" . }}"
        secret: "{{ .Values.keys.apigatewayAppSecret }}"

      hosts:
        bk-apigateway-core-api:
          addr: "http://bk-apigateway-core-api"
      {{- if not .Values.apigateway.apisixConfig.hosts }}
        bkauth:
          addr: "{{ .Values.bkAuthApiUrl }}"
        login:
          addr: "{{ .Values.bkLoginApiUrl }}"
        ssm:
          addr: "{{ .Values.bkSsmApiUrl }}"
      {{- else -}}
        {{ toYaml .Values.apigateway.apisixConfig.hosts | nindent 8 }}
      {{- end }}

    apisix:
      node_listen:
        {{- if or (eq .Values.ipFamily "ipv6") (eq .Values.ipFamily "dualstack") }}
        - ip: "[::]"
          port: {{ .Values.apigateway.service.httpPort }}
        {{- end }}
        {{- if or (eq .Values.ipFamily "ipv4") (eq .Values.ipFamily "dualstack") }}
        - ip: 0.0.0.0
          port: {{ .Values.apigateway.service.httpPort }}
        {{- end }}
      enable_ipv6: {{ or (eq .Values.ipFamily "ipv6") (eq .Values.ipFamily "dualstack") | toYaml }}

      ssl:
        listen:
          {{- if or (eq .Values.ipFamily "ipv6") (eq .Values.ipFamily "dualstack") }}
          - ip: "[::]"
            port: {{ .Values.apigateway.service.httpsPort }}
          {{- end }}
          {{- if or (eq .Values.ipFamily "ipv4") (eq .Values.ipFamily "dualstack") }}
          - ip: 0.0.0.0
            port: {{ .Values.apigateway.service.httpsPort }}
          {{- end }}
      {{- if $apisixEtcd.tlsCertSecret }}
        ssl_trusted_certificate: {{ .Values.etcdCertPath.apisix }}/{{ $apisixEtcd.caCertFileName }}
      {{- end }}

      enable_control: true
      control:
      {{- if eq .Values.ipFamily "ipv6" }}
        ip: "[::]"
      {{- else if and (eq .Values.ipFamily "dualstack") .Values.preferIpv6 }}
        ip: "[::]"
      {{- else }}
        ip: "0.0.0.0"
      {{- end }}
        port: {{ .Values.apigateway.service.controlPort }}

      router:
        http: radixtree_uri_with_parameter
      {{- with .Values.apigateway.extraApisixConfig }}{{ . | toYaml | nindent 6 }}{{ end }}

    nginx_config:
      http:
        enable_access_log: false
        client_max_body_size: "{{ .Values.apigateway.nginxConfig.httpClientMaxBodySize }}"

        {{ if .Values.apigateway.apisixConfig.luaSharedDict -}}
        lua_shared_dict:
          {{- toYaml .Values.apigateway.apisixConfig.luaSharedDict | nindent 10 }}
        {{- end }}

        custom_lua_shared_dict:
          # shared_dict for bk-plugins
          # -- bk-cache-fallback begin
          plugin-bk-cache-fallback-lock: 10m
          plugin-bk-permission: 50m
          # -- bk-cache-fallback end
        {{- if .Values.apigateway.apisixConfig.customLuaSharedDict -}}
          {{ .Values.apigateway.apisixConfig.customLuaSharedDict | toYaml | nindent 10 }}
        {{- end }}

      error_log_level: "{{ .Values.apigateway.nginxConfig.errorLogLevel }}"
      worker_processes: {{ .Values.apigateway.nginxConfig.workerProcesses }}
      {{- with .Values.apigateway.extraNginxConfig }}{{ . | toYaml | nindent 6 }}{{ end }}

    plugins: # plugin list (sorted by priority)
    {{- if .Values.apigateway.overridePlugins }}
      # ------ helm override plugins begin ------
    {{- .Values.apigateway.overridePlugins | toYaml | nindent 6 }}
      # ------ helm override plugins end ------
    {{- else -}}
      # ------ official plugins begin, related to the version 3.3.0 ------
      - real-ip # priority: 23000
      - ai # priority: 22900
      - client-control # priority: 22000
      - proxy-control # priority: 21990
      - request-id # priority: 12015
      - zipkin # priority: 12011
      #- skywalking                    # priority: 12010
      #- opentelemetry                 # priority: 12009
      - ext-plugin-pre-req # priority: 12000
      - fault-injection # priority: 11000
      - mocking # priority: 10900
      - serverless-pre-function # priority: 10000
      #- batch-requests                # priority: 4010
      - cors # priority: 4000
      - ip-restriction # priority: 3000
      - ua-restriction # priority: 2999
      - referer-restriction # priority: 2990
      - csrf # priority: 2980
      - uri-blocker # priority: 2900
      - request-validation # priority: 2800
      - openid-connect # priority: 2599
      - cas-auth # priority: 2597
      - authz-casbin # priority: 2560
      - authz-casdoor # priority: 2559
      - wolf-rbac # priority: 2555
      - ldap-auth # priority: 2540
      - hmac-auth # priority: 2530
      - basic-auth # priority: 2520
      - jwt-auth # priority: 2510
      - key-auth # priority: 2500
      - consumer-restriction # priority: 2400
      - forward-auth # priority: 2002
      - opa # priority: 2001
      - authz-keycloak # priority: 2000
      #- error-log-logger              # priority: 1091
      - body-transformer # priority: 1080
      - proxy-mirror # priority: 1010
      - proxy-cache # priority: 1009
      - proxy-rewrite # priority: 1008
      - workflow # priority: 1006
      - api-breaker # priority: 1005
      - limit-conn # priority: 1003
      - limit-count # priority: 1002
      - limit-req # priority: 1001
      #- node-status                   # priority: 1000
      - gzip # priority: 995
      - server-info # priority: 990
      - traffic-split # priority: 966
      - redirect # priority: 900
      - response-rewrite # priority: 899
      - degraphql # priority: 509
      - kafka-proxy # priority: 508
      #- dubbo-proxy                   # priority: 507
      - grpc-transcode # priority: 506
      - grpc-web # priority: 505
      - public-api # priority: 501
      - prometheus # priority: 500
      - datadog # priority: 495
      - elasticsearch-logger # priority: 413
      - echo # priority: 412
      - loggly # priority: 411
      - http-logger # priority: 410
      - splunk-hec-logging # priority: 409
      - skywalking-logger # priority: 408
      - google-cloud-logging # priority: 407
      - sls-logger # priority: 406
      - tcp-logger # priority: 405
      - kafka-logger # priority: 403
      - rocketmq-logger # priority: 402
      - syslog # priority: 401
      - udp-logger # priority: 400
      - file-logger # priority: 399
      - clickhouse-logger # priority: 398
      - tencent-cloud-cls # priority: 397
      - inspect # priority: 200
      - log-rotate                    # priority: 100
      # <- recommend to use priority (0, 100) for your custom plugins
      - example-plugin # priority: 0
      #- gm                            # priority: -43
      - aws-lambda # priority: -1899
      - azure-functions # priority: -1900
      - openwhisk # priority: -1901
      - openfunction # priority: -1902
      - serverless-post-function # priority: -2000
      - ext-plugin-post-req # priority: -3000
      - ext-plugin-post-resp # priority: -4000
      # ------ official plugins end , related to the version 3.3.0 ------
      # ------ bk plugins begin ------
      - bk-auth-validate
      - bk-auth-verify
      - bk-break-recursive-call
      - bk-concurrency-limit
      - bk-cors
      - bk-debug
      - bk-delete-cookie
      - bk-delete-sensitive
      - bk-error-wrapper
      - bk-global-rate-limit
      - bk-ip-group-restriction
      - bk-ip-restriction
      - bk-jwt
      - bk-log-context
      - bk-mock
      - bk-not-found-handler
      - bk-opentelemetry
      - bk-permission
      - bk-proxy-rewrite
      - bk-real-ip
      - bk-repl-debugger
      - bk-request-id
      - bk-resource-context
      - bk-resource-header-rewrite
      - bk-resource-rate-limit
      - bk-response-check
      - bk-stage-context
      - bk-stage-header-rewrite
      - bk-stage-global-rate-limit
      - bk-stage-rate-limit
      - bk-status-rewrite
      - bk-thunderstone
      - bk-verified-user-exempted-apps
      - bk-legacy-invalid-params
      # ------ bk plugins end ------
      {{- if .Values.apigateway.appendPlugins }}
      # ------ helm append plugins begin ------
      {{- toYaml .Values.apigateway.appendPlugins | nindent 6 }}
      # ------ helm append plugins end ------
      {{- end }}
    {{- end }}

    stream_plugins: # sorted by priority
    {{- if .Values.apigateway.overrideStreamPlugins }}
      # ------ helm override stream plugins begin ------
    {{- toYaml .Values.apigateway.overrideStreamPlugins | nindent 6 }}
      # ------ helm override stream plugins end ------
    {{- else -}}
      - ip-restriction # priority: 3000
      - limit-conn # priority: 1003
      - mqtt-proxy # priority: 1000
      #- prometheus                    # priority: 500
      - syslog # priority: 401
      # <- recommend to use priority (0, 100) for your custom plugins
      {{- if .Values.apigateway.appendStreamPlugins }}
      # ------ helm append stream plugins begin ------
      {{- toYaml .Values.apigateway.appendStreamPlugins | nindent 6 }}
      # ------ helm append stream plugins end ------
      {{- end }}
    {{- end }}

    plugin_attr:
      log-rotate:
        interval: 86400 # rotate interval (unit: second), set to 1 day
        max_kept: 7 # max number of log files will be kept, keep 7 days
        max_size: 1073741824 # max size bytes of log files to be rotated, max size 1GB
        enable_compression: false # enable log file compression(gzip) or not, default false
      prometheus:
        export_uri: /metrics
        metric_prefix: bk_apigateway_
        export_addr:
        {{- if eq .Values.ipFamily "ipv6" }}
          ip: "[::]"
        {{- else if and (eq .Values.ipFamily "dualstack") .Values.preferIpv6 }}
          ip: "[::]"
        {{- else }}
          ip: "0.0.0.0"
        {{- end }}
          port: {{ .Values.apigateway.service.metricPort }}
        default_buckets:
        {{- if .Values.apigateway.metric.default_buckets }}
        {{- toYaml .Values.apigateway.metric.default_buckets | nindent 10 }}
        {{- else }}
          - 50
          - 100
          - 500
          - 1000
          - 5000
          - 30000
          - 60000
        {{- end }}
        official:
          enable_latency: {{ .Values.apigateway.metric.official.enable_latency }}
          enable_status: {{ .Values.apigateway.metric.official.enable_status }}
          enable_bandwidth:  {{ .Values.apigateway.metric.official.enable_bandwidth }}
      bk-opentelemetry:
        enabled: {{ .Values.global.trace.enabled }}
      bk-rate-limit:
        redis_host: "{{ $apisixRedis.host }}"
        redis_port: {{ $apisixRedis.port }}
        redis_password: "{{ $apisixRedis.password }}"
        redis_database: {{ $apisixRedis.db }}
        redis_timeout: 1001
      {{- if .Values.global.trace.enabled }}
      opentelemetry:
        trace_id_source: random
        resource:
          service.name: bk-apigateway
          bk.data.token: {{ .Values.global.trace.otlp.token }}
        collector:
          address: {{ include "apigw-helm-stacks.trace.address" . }}
          request_timeout: 5
          # request_headers:
          #   Authorization: token
        batch_span_processor:
          drop_on_queue_full: true
          max_queue_size: 2048
          batch_timeout: 5
          inactive_timeout: 3
          max_export_batch_size: 64
      {{- end }}
      {{- if .Values.apigateway.appendPluginAttrs }}
      # ------ helm append plugin attrs begin ------
      {{- toYaml .Values.apigateway.appendPluginAttrs | nindent 6 }}
      # ------ helm append plugin attrs end ------
      {{- end}}

    deployment:
      role: traditional
      role_traditional:
        config_provider: etcd
      admin:
        admin_key:
          - name: bk-apigateway
            key: {{ .Values.apigateway.adminApiKey | default (randAlphaNum 32) }}
            role: admin
        allow_admin:                  # http://nginx.org/en/docs/http/ngx_http_access_module.html#allow
          {{- if or (eq .Values.ipFamily "ipv6") (eq .Values.ipFamily "dualstack") }}
          - "::/64"
          {{- end }}
          {{- if or (eq .Values.ipFamily "ipv4") (eq .Values.ipFamily "dualstack") }}
          - *********/24
          {{- end }}
        admin_listen:                # use a separate port
        {{- if eq .Values.ipFamily "ipv6" }}
          ip: "[::1]"
        {{- else if and (eq .Values.ipFamily "dualstack") .Values.preferIpv6 }}
          ip: "[::1]"
        {{- else }}
          ip: "127.0.0.1"
        {{- end }}
          port: 9180
      etcd:
        host:
          - "{{ $apisixEtcd.scheme }}://{{ $apisixEtcd.host }}:{{ $apisixEtcd.port }}"
        timeout: 120
        prefix: {{ $apisixEtcd.prefix }}

        {{- if $apisixEtcd.username }}
        user: {{ $apisixEtcd.username }}
        password: {{ $apisixEtcd.password }}
        {{ else if $apisixEtcd.tlsCertSecret }}
        tls:
          cert: {{ .Values.etcdCertPath.apisix }}/{{ $apisixEtcd.certFileName }}
          key: {{ .Values.etcdCertPath.apisix }}/{{ $apisixEtcd.keyFileName }}
          verify: false
        {{ end }}
{{- end }}
