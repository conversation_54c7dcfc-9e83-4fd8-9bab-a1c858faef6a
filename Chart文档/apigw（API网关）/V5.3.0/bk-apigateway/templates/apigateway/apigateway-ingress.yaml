{{- if and .Values.apigateway.enabled .Values.apigateway.ingress.enabled }}
{{- $backend := dict "context" . }}
{{- $_ := set $backend "serviceName" (printf "%s-apigateway" (include "apigw-helm-stacks.name-prefix" .)) }}
{{- $_ := set $backend "servicePort" .Values.apigateway.service.httpPort }}
apiVersion: {{ include "common.capabilities.ingress.apiVersion" . }}
kind: Ingress
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-apigateway
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.apigateway.ingress.annotations "context" $) | nindent 4 }}
spec: {{ include "apigw-helm-stacks.ingress-class" . | nindent 2 }}
  rules:
  - host: {{ include "apigw-helm-stacks.ingress-host" (list .Values.apigateway.ingress .Values.global) }}
    http:
      paths:
      - backend: {{ include "common.ingress.backend" $backend | nindent 10 }}
        path: {{ .Values.apigateway.ingress.path | quote }}
        {{- if eq "true" (include "common.ingress.supportsPathType" .) }}
        pathType: Prefix
        {{- end }}
      {{- if and .Values.bkEsb.enabled .Values.bkEsb.ingress.enabled }}
      - backend: {{ include "common.ingress.backend" $backend | nindent 10 }}
        path: {{ .Values.bkEsb.ingress.path | quote }}
        {{- if eq "true" (include "common.ingress.supportsPathType" .) }}
        pathType: Prefix
        {{- end }}
      {{- end }}
{{- end }}