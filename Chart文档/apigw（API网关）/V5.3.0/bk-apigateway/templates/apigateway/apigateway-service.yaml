{{- if .Values.apigateway.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-apigateway
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
    app.kubernetes.io/component: "apigateway"
spec:
  type: {{ .Values.apigateway.service.type }}
  selector: {{ include "apigw-helm-stacks.service-selector-labels" . | nindent 4 }}
    app.kubernetes.io/component: "apigateway"
  ports:
    - name: http
      port: {{ .Values.apigateway.service.httpPort }}
      protocol: TCP
      targetPort: http
    - name: https
      port: {{ .Values.apigateway.service.httpsPort }}
      protocol: TCP
      targetPort: https
    - name: metric
      port: {{ .Values.apigateway.service.metricPort }}
      protocol: TCP
      targetPort: metric
    - name: control
      port: {{ .Values.apigateway.service.controlPort }}
      protocol: TCP
      targetPort: control
{{- end }}