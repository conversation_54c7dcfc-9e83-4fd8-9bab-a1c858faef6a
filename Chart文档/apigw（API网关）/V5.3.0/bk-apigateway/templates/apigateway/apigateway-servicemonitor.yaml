{{- if and .Values.apigateway.enabled .Values.apigateway.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-apigateway
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels: {{ include "apigw-helm-stacks.labels" . | nindent 6 }}
      app.kubernetes.io/component: "apigateway"
  endpoints:
    - port: metric
      path: "/metrics"
      interval: "{{ .Values.apigateway.serviceMonitor.interval }}"
      scrapeTimeout: "{{ .Values.apigateway.serviceMonitor.scrapeTimeout }}"
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
{{- end }}