{{- if and .Values.apigateway.enabled .Values.apigateway.bkLogConfig.enabled }}
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-apigateway-stdout
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  dataId: {{ .Values.apigateway.bkLogConfig.stdoutDataId }}
  encoding: {{ .Values.apigateway.bkLogConfig.stdoutEncoding }}
  logConfigType: std_log_config
  namespace: {{ .Release.Namespace }}
  labelSelector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "apigateway"
---
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-apigateway-file
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  dataId: {{ .Values.apigateway.bkLogConfig.fileDataId | default .Values.apigateway.bkLogConfig.stdoutDataId }}
  encoding: {{ .Values.apigateway.bkLogConfig.fileEncoding | default .Values.apigateway.bkLogConfig.stdoutEncoding }}
  logConfigType: container_log_config
  namespace: {{ .Release.Namespace }}
  labelSelector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "apigateway"
  path:
    - /usr/local/apisix/logs/error.log
---
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-apigateway-container
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  dataId: {{ .Values.apigateway.bkLogConfig.containerDataId }}
  encoding: {{ .Values.apigateway.bkLogConfig.containerEncoding }}
  logConfigType: container_log_config
  namespace: {{ .Release.Namespace }}
  labelSelector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "apigateway"
  path:
    - /usr/local/apisix/logs/access.log
{{- end }}
