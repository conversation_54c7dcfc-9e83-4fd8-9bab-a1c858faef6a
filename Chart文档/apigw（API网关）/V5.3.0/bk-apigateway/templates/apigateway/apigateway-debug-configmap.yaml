{{- if .Values.apigateway.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-apisix-debug-config
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
data:
  debug.yaml: |
    basic:
      enable: {{ .Values.apigateway.apisixDebug.enabled }}
    http_filter:
      enable: {{ .Values.apigateway.apisixDebug.enabled }}
      enable_header_name: X-BKAPI-Dynamic-Debug
    hook_conf:
      enable: {{ .Values.apigateway.apisixDebug.enabled }}
      name: hook_phase
      log_level: warn
      is_print_input_args: true
      is_print_return_value: true

    {{ if .Values.apigateway.apisixDebug.hookPhase -}}
    {{- $hook_phase := dict "hook_phase" .Values.apigateway.apisixDebug.hookPhase }}
    {{- $hook_phase | toYaml | nindent 4 }}
    {{- else }}
    hookPhase: {}
    {{- end }}

    #END
{{- end }}
