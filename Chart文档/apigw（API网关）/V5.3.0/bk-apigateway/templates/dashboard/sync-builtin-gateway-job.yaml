{{- if .Values.dashboard.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "apigw-helm-stacks.job-name" (list . "sync-builtin-gateway") }}
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  backoffLimit: 10
  parallelism: 1
  template:
    metadata:
      annotations: {{ include "apigw-helm-stacks.pod-annotations" . | nindent 8 }}
      labels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 8 }}
        app.kubernetes.io/component: "sync-builtin-gateway"
    spec:
      serviceAccountName: {{ include "apigw-helm-stacks.service-account-name" . }}
      affinity: {{ include "apigw-helm-stacks.affinity" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      tolerations: {{ include "apigw-helm-stacks.tolerations" . | nindent 8 }}
      hostAliases: {{ include "apigw-helm-stacks.host-aliases" . | nindent 8 }}
      initContainers: {{ include "apigw-helm-stacks.wait-for-migrations-init-container" . | nindent 8 }}
      containers:
      - command:
        - bash
        - -c
        args:
        - |
          set -ex
          {{ range $key, $info := .Values.dashboard.builtinGateway }}# for gateway {{ $key }}
          python manage.py sync_api --name "{{ $key }}"
          python manage.py update_jwt_key --api-name "{{ $key }}" --private-key "{{ $info.privateKeyBase64 }}" --public-key "{{ $info.publicKeyBase64 }}"
          python manage.py add_related_app --api-name "{{ $key }}" --app-code "{{ $info.appCode }}"
          {{ end }}

        env: {{- include "apigw-helm-stacks.dashboard.envs" . | nindent 10 }}
        envFrom:
        - configMapRef:
            name: {{ include "apigw-helm-stacks.name-prefix" . }}-basic-env
        - secretRef:
            name: {{ include "apigw-helm-stacks.name-prefix" . }}-encrypt-key
        image: {{ include "apigw-helm-stacks.image" (list  .Values.dashboard.image .Values.global) }}
        imagePullPolicy: {{ include "apigw-helm-stacks.image-pull-policy" .Values.dashboard.image }}
        name: bk-apigateway-sync-builtin-gateway
        resources: {{ include "apigw-helm-stacks.container-resources" .Values.dashboard | nindent 10 }}
        volumeMounts: {{- include "apigw-helm-stacks.dashboard.volume-mounts" . | nindent 10 }}
      imagePullSecrets: {{ include "apigw-helm-stacks.image-pull-secrets" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      restartPolicy: Never
      securityContext: {{ include "apigw-helm-stacks.container-security-context" . | nindent 8 }}
      volumes: {{- include "apigw-helm-stacks.dashboard.volumes" . | nindent 8 }}
  ttlSecondsAfterFinished: 86400
{{- end }}