{{- if .Values.dashboard.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-dashboard
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
    app.kubernetes.io/component: "dashboard"
spec:
  type: {{ .Values.dashboard.service.type }}
  selector: {{ include "apigw-helm-stacks.service-selector-labels" . | nindent 4 }}
    app.kubernetes.io/component: "dashboard"
  ports:
    - name: http
      port: {{ .Values.dashboard.service.httpPort }}
      protocol: TCP
      targetPort: http
{{- end }}