{{/*
默认 Elasticsearch 基础配置，处理了内建和外部 Elasticsearch 场景
*/}}
{{- define "apigw-helm-stacks.elasticsearch" -}}
{{- $root := first . -}}
{{- $name := last . -}}
{{- $values := $root.Values -}}
{{- $es := index $values.externalElasticsearch $name -}}
{{- $esDefault := $values.externalElasticsearch.default -}}

user: {{ $es.user | default $esDefault.user }}
password: {{ $es.password | default $esDefault.password }}
host: {{ $es.host | default $esDefault.host }}
port: {{ $es.port | default $esDefault.port }}
{{- end -}}

{{/*
apigw Elasticsearch 配置，处理默认 Elasticsearch 合并逻辑
*/}}
{{- define "apigw-helm-stacks.elasticsearch.apigw" -}}
{{ include "apigw-helm-stacks.elasticsearch" (list . "apigw") }}
{{- end -}}
