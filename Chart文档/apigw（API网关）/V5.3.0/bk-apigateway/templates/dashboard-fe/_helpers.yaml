{{/*
apigateway dashboard-fe ingress backend
*/}}
{{- define "apigw-helm-stacks.dashboard-fe.ingress-backend" -}}
{{- $backend := dict "context" . -}}
{{- $_ := set $backend "serviceName" (printf "%s-dashboard-fe" (include "apigw-helm-stacks.name-prefix" .)) -}}
{{- $_ := set $backend "servicePort" .Values.dashboardFe.service.httpPort -}}
{{ include "common.ingress.backend" $backend }}
{{- end -}}

{{/*
apigateway dashboard-fe ingress url
*/}}
{{- define "apigw-helm-stacks.dashboard-fe.ingress-url" -}}
{{- $dashboardFeIngress := .Values.dashboardFe.ingress -}}
{{- $_ := set $dashboardFeIngress "name" .Values.dashboard.ingress.name -}}
{{- include "apigw-helm-stacks.ingress-url" (list $dashboardFeIngress .Values.global) -}}
{{- end -}}