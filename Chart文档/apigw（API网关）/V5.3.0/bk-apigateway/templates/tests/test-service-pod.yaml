{{- if and .Values.apigateway.enabled .Values.apigateway.bkapiServiceName }}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "apigw-helm-stacks.name-prefix" . }}-test-service"
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
    app.kubernetes.io/component: "test"
  annotations:
    "helm.sh/hook": test
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  containers:
    - name: test
      image: {{ include "apigw-helm-stacks.image" (list  .Values.k8sWaitFor.image .Values.global) }}
      imagePullPolicy: {{ .Values.k8sWaitFor.image.pullPolicy }}
      resources: {{ toYaml .Values.k8sWaitFor.resources | nindent 12 }}
      command:
      - curl
      args: ["-vv", "-f", "http://{{ .Values.apigateway.bkapiServiceName }}/api/bk-default/prod/ping"]
  restartPolicy: Never
{{- end }}