{{- if .Values.apigateway.enabled }}
apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "apigw-helm-stacks.name-prefix" . }}-test-apigateway"
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
    app.kubernetes.io/component: "test"
  annotations:
    "helm.sh/hook": test
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  containers:
    - name: test
      image: {{ include "apigw-helm-stacks.image" (list  .Values.k8sWaitFor.image .Values.global) }}
      imagePullPolicy: {{ .Values.k8sWaitFor.image.pullPolicy }}
      resources: {{ toYaml .Values.k8sWaitFor.resources | nindent 12 }}
      command:
      - curl
      args: ["-vv", "-f", "http://{{ include "apigw-helm-stacks.name-prefix" . }}-apigateway:{{ .Values.apigateway.service.httpPort }}/healthz"]
  restartPolicy: Never
{{- end }}