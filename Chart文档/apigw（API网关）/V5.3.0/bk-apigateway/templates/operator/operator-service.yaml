{{- if .Values.operator.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-operator
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
    app.kubernetes.io/component: "operator"
spec:
  type: {{ .Values.operator.service.type }}
  selector: {{ include "apigw-helm-stacks.service-selector-labels" . | nindent 4 }}
    app.kubernetes.io/component: "operator"
  ports:
    - name: http
      port: {{ .Values.operator.service.httpPort }}
      protocol: TCP
      targetPort: http
{{- end }}
