{{- if .Values.operator.enabled }}
{{- $apigwEtcd := fromYaml (include "apigw-helm-stacks.etcd.apigw" .) -}}
{{- $apisixEtcd := fromYaml (include "apigw-helm-stacks.etcd.apisix" .) -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-operator-config
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
data:
  config.yaml: |-
    debug: true
    operator:
      withKube: true
      withLeader: true
      agentMode: true

      defaultGateway: "{{ include "apigw-helm-stacks.micro-gateway.api-name" . }}"
      defaultStage: "{{ include "apigw-helm-stacks.micro-gateway.stage-name" . }}"

      etcdPutInterval: {{ .Values.operator.publish.etcdPutInterval }}

    dashboard:
      etcd:
        endpoints: "{{ $apigwEtcd.host }}:{{ $apigwEtcd.port }}"
        keyPrefix: "{{ $apigwEtcd.prefix }}/{{ include "apigw-helm-stacks.micro-gateway.stage-name" . }}"
        {{- if $apigwEtcd.username  }}
        username: "{{ $apigwEtcd.username }}"
        password: "{{ $apigwEtcd.password }}"
        {{- end }}
        {{- if $apigwEtcd.tlsCertSecret }}
        cACert: "{{ .Values.etcdCertPath.operator }}/{{ $apigwEtcd.caCertFileName }}"
        cert: "{{ .Values.etcdCertPath.operator }}/{{ $apigwEtcd.certFileName }}"
        key: "{{ .Values.etcdCertPath.operator }}/{{ $apigwEtcd.keyFileName }}"
        {{- end }}

    apisix:
      etcd:
        endpoints: "{{ $apisixEtcd.host }}:{{ $apisixEtcd.port }}"
        keyPrefix: "{{ $apisixEtcd.prefix }}"
        {{- if $apisixEtcd.username  }}
        username: "{{ $apisixEtcd.username }}"
        password: "{{ $apisixEtcd.password }}"
        {{- end }}
        {{- if $apisixEtcd.tlsCertSecret }}
        cACert: "{{ .Values.etcdCertPath.apisix }}/{{ $apisixEtcd.caCertFileName }}"
        cert: "{{ .Values.etcdCertPath.apisix }}/{{ $apisixEtcd.certFileName }}"
        key: "{{ .Values.etcdCertPath.apisix }}/{{ $apisixEtcd.keyFileName }}"
        {{- end }}
      resourceStoreMode: "etcd"
      virtualStage:
        operatorExternalHost: "{{ include "apigw-helm-stacks.name-prefix" . }}-operator"
        operatorExternalHealthProbePort: {{ .Values.operator.service.httpPort }}
        extraApisixResources: "/data/config/extra-resources.yaml"
    instance:
        id: "{{ include "apigw-helm-stacks.micro-gateway.instance-id" . }}"
        secret: "{{ .Values.keys.apigatewayAppSecret }}"
     
    eventReporter:
      coreAPIHost:  "http://bk-apigateway-core-api"
      apisixHost: "http://bk-apigateway-apigateway:{{ .Values.apigateway.service.httpPort }}"
      versionProbe:
        timeout: {{ .Values.operator.eventReporter.versionProbe.timeout }}
        bufferSize: {{ .Values.operator.eventReporter.versionProbe.bufferSize }}
        retry: 
          count: {{ .Values.operator.eventReporter.versionProbe.retry.count }}
          interval: {{ .Values.operator.eventReporter.versionProbe.retry.interval }}
      eventBufferSize: {{ .Values.operator.eventReporter.eventBufferSize }}
      reporterBufferSize: {{ .Values.operator.eventReporter.reporterBufferSize }}

    httpServer:
      {{- if or (eq .Values.ipFamily "ipv4") (eq .Values.ipFamily "dualstack") }}
      bindAddress: "0.0.0.0"
      {{- end }}
      {{- if or (eq .Values.ipFamily "ipv6") (eq .Values.ipFamily "dualstack") }}
      bindAddressV6: "[::]"
      {{- end }}
      bindPort: {{ .Values.operator.service.httpPort }}

    {{- if .Values.operator.sentry.dsn }}
    sentry:
      dsn: {{ .Values.operator.sentry.dsn }}
      reportLevel: {{ .Values.operator.sentry.reportLogLevel }}
    {{- end }}

    logger:
      default:
        level: info
        writer: os
        settings: {name: stdout}
      controller:
        level: info
        writer: file
        settings: {name: controller.log, size: 100, backups: 10, age: 7, path: /app/logs/}

    tracing:
      enable: {{ .Values.global.trace.enabled }}
      endpoint: {{ .Values.global.trace.otlp.host }}:{{ .Values.global.trace.otlp.port }}
      type: {{ .Values.global.trace.otlp.type }}
      token: {{ .Values.global.trace.otlp.token }}
      sampler: {{ .Values.operator.trace.sampler }}
      samplerRatio: {{ .Values.operator.trace.samplerRatio }}
      serviceName: {{ .Values.operator.trace.serviceName }}
  extra-resources.yaml: |-
    routes:
      {{- if .Values.bkEsb.enabled }}
      - status: 1
        id: _bk-esb-buffet-legacy-route
        name: _bk-esb-buffet-legacy-route
        uri: "/api/c/self-service-api/*"
        {{- if .Values.apigateway.service.timeout }}
        timeout:
          connect: {{ default 60 .Values.apigateway.service.timeout.connect }}
          send:  {{ default 60 .Values.apigateway.service.timeout.send }}
          read:  {{ default 60 .Values.apigateway.service.timeout.read }}
        {{- end }}
        upstream:
          type: roundrobin
          pass_host: pass
          nodes:
            "localhost:{{ .Values.apigateway.service.httpPort }}": 1
        plugins:
          proxy-rewrite:
            regex_uri: ["/api/c/self-service-api/(.*)", "/api/bk-esb-buffet/prod/$1"]
      - status: 1
        id: _bk-esb-compapi-legacy-route
        name: _bk-esb-compapi-legacy-route
        uri: "/api/c/compapi/*"
        {{- if .Values.apigateway.service.timeout }}
        timeout:
          connect: {{ default 60 .Values.apigateway.service.timeout.connect }}
          send:  {{ default 60 .Values.apigateway.service.timeout.send }}
          read:  {{ default 60 .Values.apigateway.service.timeout.read }}
        {{- end }}
        upstream:
          type: roundrobin
          pass_host: pass
          nodes:
            "localhost:{{ .Values.apigateway.service.httpPort }}": 1
        plugins:
          proxy-rewrite:
            regex_uri: ["/api/c/compapi/(.*)", "/api/bk-esb/prod/$1"]
      {{- end }}
      {{- with .Values.operator.extraRoutes }}
      {{- include "common.tplvalues.render" (dict "value" . "context" $) | nindent 6 }}
      {{- end }}
{{- end }}
