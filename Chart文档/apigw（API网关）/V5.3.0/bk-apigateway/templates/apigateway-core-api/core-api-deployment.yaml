{{- if .Values.apigatewayCoreApi.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "apigw-helm-stacks.apigatewayCoreApi.name" . }}
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  replicas: {{ include "apigw-helm-stacks.deployment-replicas" (list .Values.apigatewayCoreApi .Values) }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 0
  selector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "apigateway-core-api"
  template:
    metadata:
      annotations:
        checksum/configmap: {{ include (print $.Template.BasePath "/apigateway-core-api/core-api-configmap.yaml") . | sha256sum }}
        {{- if .Values.podAnnotations  }}
        {{- include "apigw-helm-stacks.pod-annotations" . | nindent 8 }}
        {{- end }}
      labels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 8 }}
        app.kubernetes.io/component: "apigateway-core-api"
    spec:
      serviceAccountName: {{ include "apigw-helm-stacks.service-account-name" . }}
      affinity: {{ include "apigw-helm-stacks.affinity" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      tolerations: {{ include "apigw-helm-stacks.tolerations" . | nindent 8 }}
      hostAliases: {{ include "apigw-helm-stacks.host-aliases" . | nindent 8 }}
      initContainers:
        {{- include "apigw-helm-stacks.wait-for-storages-job-init-container" . | nindent 8 }}
      containers:
      - name: bk-apigateway-core-api
        image: {{ include "apigw-helm-stacks.image" (list  .Values.apigatewayCoreApi.image .Values.global) }}
        imagePullPolicy: {{ include "apigw-helm-stacks.image-pull-policy" .Values.apigatewayCoreApi.image }}
        command: ["/bin/bash"]
        {{- if and .Values.encrypt .Values.encrypt.args .Values.encrypt.args.coreApiDeployment }}
        args: ["-c", {{ .Values.encrypt.args.coreApiDeployment | quote }}]
        {{- else }}
        args: ["-c", "/app/bk-apigateway-core-api -c /app/config.yaml"]
        {{- end }}
        {{- if .Values.apigatewayCoreApi.livenessProbe.enabled }}
        livenessProbe:
          httpGet:
            path: /ping
            port: http
          initialDelaySeconds: {{ .Values.apigatewayCoreApi.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.apigatewayCoreApi.livenessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.apigatewayCoreApi.livenessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.apigatewayCoreApi.livenessProbe.successThreshold }}
          failureThreshold: {{ .Values.apigatewayCoreApi.livenessProbe.failureThreshold }}
        {{- end }}
        {{- if .Values.apigatewayCoreApi.readinessProbe.enabled }}
        readinessProbe:
          httpGet:
            path: /healthz
            port: http
          initialDelaySeconds: {{ .Values.apigatewayCoreApi.readinessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.apigatewayCoreApi.readinessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.apigatewayCoreApi.readinessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.apigatewayCoreApi.readinessProbe.successThreshold }}
          failureThreshold: {{ .Values.apigatewayCoreApi.readinessProbe.failureThreshold }}
        {{- end }}
        env: {{ include "apigw-helm-stacks.yaml" .Values.apigatewayCoreApi.extraEnvVars | nindent 8 }}
        ports:
          - name: http
            containerPort: {{ .Values.apigatewayCoreApi.service.httpPort }}
            protocol: TCP
        lifecycle:
          preStop:
            exec:
              command: ["/bin/bash", "-c", "sleep 15"]  
        resources: {{ include "apigw-helm-stacks.container-resources" .Values.apigatewayCoreApi | nindent 10 }}
        volumeMounts: {{ include "apigw-helm-stacks.yaml" .Values.apigatewayCoreApi.extraVolumeMounts | nindent 8 }}
        - mountPath: /app/config.yaml
          name: apigateway-core-api-config
          subPath: config.yaml
      imagePullSecrets: {{ include "apigw-helm-stacks.image-pull-secrets" . | nindent 8 }}
      securityContext: {{ include "apigw-helm-stacks.container-security-context" . | nindent 8 }}
      restartPolicy: Always
      volumes: {{ include "apigw-helm-stacks.yaml" .Values.apigatewayCoreApi.extraVolumes | nindent 6 }}
      - name: apigateway-core-api-config
        configMap:
          name: {{ include "apigw-helm-stacks.name-prefix" . }}-core-api-config
          items:
          - key: config.yaml
            path: config.yaml
{{- end }}
