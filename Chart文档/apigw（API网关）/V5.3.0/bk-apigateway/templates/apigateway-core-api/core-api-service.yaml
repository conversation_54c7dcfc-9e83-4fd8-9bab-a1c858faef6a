{{- if .Values.apigatewayCoreApi.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-core-api
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
    app.kubernetes.io/component: "apigateway-core-api"
spec:
  type: {{ .Values.apigatewayCoreApi.service.type }}
  selector: {{ include "apigw-helm-stacks.service-selector-labels" . | nindent 4 }}
    app.kubernetes.io/component: "apigateway-core-api"
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: http
{{- end }}