{{- if .Values.apigatewayCoreApi.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-core-api-config
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
data:
  config.yaml: |-
    debug: {{ .Values.apigatewayCoreApi.debug }}

    server:
      host: 0.0.0.0
      port: {{ .Values.apigatewayCoreApi.service.httpPort }}

      readTimeout: 60
      writeTimeout: 60
      idleTimeout: 180

    sentry:
      dsn: {{ .Values.apigatewayCoreApi.sentry.dsn }}
      reportLogLevel: {{ .Values.apigatewayCoreApi.sentry.reportLogLevel }}
    databases:
      {{- $apigwDB := fromYaml (include "apigw-helm-stacks.databaseApigw" .) }}
      - id: "apigateway"
        host: "{{ $apigwDB.host }}"
        port: {{ $apigwDB.port }}
        user: "{{ $apigwDB.user }}"
        password: "{{ $apigwDB.password }}"
        name: "{{ $apigwDB.name }}"
        maxOpenConns: 200
        maxIdleConns: 50
        connMaxLifetimeSecond: 600

    logger:
      default:
        level: {{ .Values.apigatewayCoreApi.logLevel }}
        writer: os
        buffered: false
        settings: {name: stdout}
      api:
        level: {{ .Values.apigatewayCoreApi.logLevel }}
        writer: file
        buffered: true
        settings: {name: /app/logs/core_api.log, size: 200, backups: 10, age: 7, path: ./}
    tracing:
      enable: {{ .Values.global.trace.enabled }}
      endpoint: {{ .Values.global.trace.otlp.host }}:{{ .Values.global.trace.otlp.port }}
      type: {{ .Values.global.trace.otlp.type }}
      token: {{ .Values.global.trace.otlp.token }}
      sampler: {{ .Values.apigatewayCoreApi.trace.sampler }}
      samplerRatio: {{ .Values.apigatewayCoreApi.trace.samplerRatio }}
      serviceName: {{ .Values.apigatewayCoreApi.trace.serviceName }}
      instrument:
        ginAPI: {{ .Values.apigatewayCoreApi.trace.instrument.ginApi }}
        dbAPI: {{ .Values.apigatewayCoreApi.trace.instrument.dbApi }}
{{- end }}
