{{- if and .Values.apigatewayCoreApi.enabled .Values.apigatewayCoreApi.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-apigateway-core-api
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels: {{ include "apigw-helm-stacks.labels" . | nindent 6 }}
      app.kubernetes.io/component: "apigateway-core-api"
  endpoints:
    - port: http
      path: "/metrics"
      interval: "{{ .Values.apigatewayCoreApi.serviceMonitor.interval }}"
      scrapeTimeout: "{{ .Values.apigatewayCoreApi.serviceMonitor.scrapeTimeout }}"
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
{{- end }}