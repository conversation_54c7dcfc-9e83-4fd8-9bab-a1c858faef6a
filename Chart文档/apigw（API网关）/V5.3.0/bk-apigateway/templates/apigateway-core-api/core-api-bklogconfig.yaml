{{- if and .Values.apigatewayCoreApi.enabled .Values.apigatewayCoreApi.bkLogConfig.enabled }}
# stdout Log
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-apigateway-core-api-stdout
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  dataId: {{ .Values.apigatewayCoreApi.bkLogConfig.stdoutDataId }}
  encoding: {{ .Values.apigatewayCoreApi.bkLogConfig.stdoutEncoding }}
  logConfigType: std_log_config
  namespace: {{ .Release.Namespace }}
  labelSelector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "apigateway-core-api"
---
# Container Log
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-apigateway-core-api-file
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  dataId: {{ .Values.apigatewayCoreApi.bkLogConfig.fileDataId | default .Values.apigatewayCoreApi.bkLogConfig.stdoutDataId }}
  encoding: {{ .Values.apigatewayCoreApi.bkLogConfig.fileEncoding | default .Values.apigatewayCoreApi.bkLogConfig.stdoutEncoding }}
  logConfigType: container_log_config
  namespace: {{ .Release.Namespace }}
  labelSelector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "apigateway-core-api"
  path:
    - /app/logs/*.log
{{- end }}
