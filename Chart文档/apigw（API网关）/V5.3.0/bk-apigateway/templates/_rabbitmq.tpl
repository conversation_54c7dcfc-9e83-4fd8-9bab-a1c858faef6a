{{/*
默认 Rabbitmq 基础配置，处理了外部 Rabbitmq 场景
*/}}
{{- define "apigw-helm-stacks.rabbitmq" -}}
{{- $root := first . -}}
{{- $name := last . -}}
{{- $values := $root.Values -}}
{{- $rabbitmq := index $values.externalRabbitmq $name -}}
{{- $rabbitmqDefault := $values.externalRabbitmq.default -}}

host: {{ $rabbitmq.host | default $rabbitmqDefault.host }}
port: {{ $rabbitmq.port | default $rabbitmqDefault.port }}
username: {{ $rabbitmq.username | default $rabbitmqDefault.username }}
password: {{ $rabbitmq.password | default $rabbitmqDefault.password }}
vhost: {{ $rabbitmq.vhost | default $rabbitmqDefault.vhost }}
{{- end -}}

{{/*
apigw Rabbitmq 配置，处理默认 Rabbitmq 合并逻辑
*/}}
{{- define "apigw-helm-stacks.rabbitmq.apigw" -}}
{{ include "apigw-helm-stacks.rabbitmq" (list . "apigw") }}
{{- end -}}
