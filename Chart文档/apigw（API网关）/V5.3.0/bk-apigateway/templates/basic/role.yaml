
{{- if and .Values.rbac.create }}
apiVersion: {{ include "common.capabilities.rbac.apiVersion" . }}
kind: Role
metadata:
  name: {{ include "apigw-helm-stacks.service-account-name" . }}
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
rules:
  - apiGroups:
      - ""
      - batch
      - apps
      - extensions
    resources:
      - jobs
      - pods
      - services
    verbs:
      - get
      - list
      - watch
  - apiGroups:
    - gateway.bk.tencent.com
    - coordination.k8s.io
    resources:
    - '*'
    verbs:
    - '*'
{{- end }}
