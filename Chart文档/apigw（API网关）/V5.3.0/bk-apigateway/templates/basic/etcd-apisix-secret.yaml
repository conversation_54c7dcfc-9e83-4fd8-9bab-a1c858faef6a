{{- if .Values.apigateway.enabled }}
{{- $apisixEtcd := fromYaml (include "apigw-helm-stacks.etcd.apisix" .) -}}
{{- if $apisixEtcd.certBase64Encoded }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $apisixEtcd.tlsCertSecret }}
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
data:
  {{- if $apisixEtcd.caBase64Encoded }}
  {{ $apisixEtcd.caCertFileName }}: {{ $apisixEtcd.caBase64Encoded }}
  {{- end }}
  {{- if $apisixEtcd.certBase64Encoded }}
  {{ $apisixEtcd.certFileName }}: {{ $apisixEtcd.certBase64Encoded }}
  {{- end }}
  {{- if $apisixEtcd.keyBase64Encoded }}
  {{ $apisixEtcd.keyFileName }}: {{ $apisixEtcd.keyBase64Encoded }}
  {{- end }}
{{- end }}
{{- end }}
