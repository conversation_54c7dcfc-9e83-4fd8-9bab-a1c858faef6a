{{- if .Values.mariadb.enabled -}}
{{- $apigwDB := fromYaml (include "apigw-helm-stacks.databaseApigw" .) -}}
{{- $esbDB := fromYaml (include "apigw-helm-stacks.databaseEsb" .) -}}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.mariadb.initdbScriptsConfigMap }}
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
data:
  init.sql: |
    CREATE DATABASE IF NOT EXISTS `{{ $apigwDB.name }}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    CREATE DATABASE IF NOT EXISTS `{{ $esbDB.name }}` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
    GRANT ALL PRIVILEGES ON `{{ $apigwDB.name }}`.* TO `{{ $apigwDB.user }}`@'%';
    GRANT ALL PRIVILEGES ON `{{ $esbDB.name }}`.* TO `{{ $esbDB.user }}`@'%';
{{- end -}}