{{- if and .Values.rbac.create }}
apiVersion: {{ include "common.capabilities.rbac.apiVersion" . }}
kind: RoleBinding
metadata:
  name: {{ include "apigw-helm-stacks.service-account-name" . }}
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "apigw-helm-stacks.service-account-name" . }}
subjects:
  - kind: ServiceAccount
    name: {{ include "apigw-helm-stacks.service-account-name" . }}
{{- end }}