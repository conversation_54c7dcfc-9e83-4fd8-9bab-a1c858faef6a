global:
  ## 源地址，优先级最高
  ##
  imageRegistry: ""

  ## 拉取镜像密钥
  ##
  imagePullSecrets:
  #  - name: myRegistryKeySecretName

  ## 根域名，将会根据这个值生成 Ingress 的 Host
  ##
  bkDomain: "example.com"

  ## 域名协议，用于展示，不影响 ingress
  ##
  bkDomainScheme: "http"

  ## Kubernetes 集群版本
  ##
  kubeVersion: ""

  ## 域名别名
  ##
  hostAliases: []

  ## 蓝鲸调用链
  ##
  trace:
    enabled: false
    otlp:
      host: 127.0.0.1
      port: 4318
      token: ""
      ## 只支持 http, 不支持 grpc
      ##
      type: http

## 默认进程副本数
##
replicaCount: 1

## 覆盖默认名称（替代名称 RELEASE-NAME 前缀）
##
nameOverride: ""

## 完整替代名称
##
fullnameOverride: ""

## pod 注解
##
podAnnotations: {}

## Ingress 类名
##
ingressClassName: ""

## 容器安全性
##
securityContext:
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

## 节点选择器
##
nodeSelector: {}

## 污点配置
##
tolerations: []

## 亲和性配置
##
affinity: {}

## 是否开启调试
##
debug: false

## 启用 sentry
sentryDsn: ""

## 加密密钥，敏感；配置后不可修改，否则会导致加密数据异常
##
encryptKey: ""

## 网关管理员
##
managers: ["admin"]

## 是否创建网关 CRD
##
registerCrds: true

## 默认微网关实例 ID
##
defaultMicroGatewayInstanceId: ""

## 地址监听模式
## 可选项：ipv4、ipv6、dualstack
ipFamily: dualstack

## 在双栈模式下，诸如 APISIX Metric 暴露服务等暂时无法进行双栈监听的服务，
## 优先使用何种 IP 协议。
##
preferIpv6: false

## 外部数据库配置
##
externalDatabase:
  ## apigateway 数据库
  ##
  apigw:
    ## 数据库名称，不建议修改
    ##
    name: "bk_apigateway"
    ## 连接地址
    ##
    host: "mysql.example.com"
    ## 连接端口
    ##
    port: 3306
    ## 用户名（请为用户授权访问 bk_apigateway 数据库）
    ##
    user: "bk_apigateway"
    ## 明文方式的密码
    ##
    password: ""

  ## 新版 esb 数据库（不可复用旧 esb 数据库）
  ##
  esb:
    ## 数据库名称，不建议修改
    ##
    name: "bk_esb"
    ## 连接地址
    ##
    host: "mysql.example.com"
    ## 连接端口
    ##
    port: 3306
    ## 用户名（请为用户授权访问 bk_esb 数据库）
    ##
    user: "bk_apigateway"
    ## 明文方式的密码
    ##
    password: ""

  ## legacyEsb 数据库
  ##
  legacyEsb:
    ## 仅在需要同步旧版 ESB 数据时，才需要配置
    ##
    name: "open_paas"
    ## 连接地址，默认为集群内部数据库
    ##
    host: ""
    ## 数据库端口
    ##
    port: 3306
    ## 用户名（请为用户授权访问 open_paas 数据库）
    ##
    user: "bk_apigateway"
    ## 数据库密码
    ##
    password: ""

## 内建数据库
##
mariadb:
  ## 是否启用，关闭后将会使用 externalDatabase 配置
  ##
  enabled: true

  ## 实例名称，不建议修改
  ##
  nameOverride: "mariadb"

  architecture: standalone

  auth:
    username: "bk-apigateway"
    password: "blueking"
    rootPassword: "blueking"

  primary:
    ## 服务配置
    ##
    service:
      port: 3306

    persistence:
      enabled: true
      size: 10Gi

  ## 镜像配置
  ##
  image:
    registry: docker.io
    repository: bitnami/mariadb
    ## 镜像标签
    ##
    tag: 10.5.11-debian-10-r34

  initdbScriptsConfigMap: "bk-apigateway-init-sql"

## 外部 Redis
##
externalRedis:
  ## 默认 Redis 连接信息
  ##
  default:
    ## 连接地址
    ##
    host: "redis.example.com"
    ## 连接端口
    ##
    port: 6379
    ## 明文方式的密码
    ##
    password: ""

    ## 数据库编号
    ##
    db: 0
    ## Redis pool size
    ##
    poolSize: 300
    # NOTE: we remove sentinel support from 0.7.x! so if you have configured sentinel here, change to standalone

## 内建 Redis
##
redis:
  ## 是否启用，关闭后将会使用 externalRedis 配置
  ##
  enabled: true

  ## 实例名称，不建议修改
  ##
  nameOverride: "redis"
  architecture: standalone

  auth:
    password: blueking

  master:
    service:
      port: 6379
    persistence:
      enabled: false

  replica:
    replicaCount: 1
    persistence:
      enabled: false

  ## 镜像配置
  ##
  image:
    registry: docker.io
    repository: bitnami/redis
    ## 镜像标签
    ##
    tag: 6.2.5-debian-10-r11

## 外部 Rabbitmq 配置
##
externalRabbitmq:
  ## 默认 Rabbitmq 连接信息
  ## 如未完成配置，则使用 redis 作为 celery broker
  ##
  default:
    ## 连接地址
    ##
    host: "rabbitmq.example.com"
    ## 连接端口
    ##
    port: 5672
    ## 用户名
    ##
    username: "bk_apigateway"
    ## 明文方式的密码
    ##
    password: ""
    ## virtual host
    ##
    vhost: "bk_apigateway"

## 外部 Elasticsearch 配置
externalElasticsearch:
  ## 默认 Elasticsearch 连接信息
  ##
  default:
    ## 连接地址
    ##
    host: "elasticsearch.example.com"
    ## 连接端口
    ##
    port: 9200
    ## 用户名
    ##
    user: "bk_apigateway"
    ## 明文方式的密码
    ##
    password: ""

## ETCD 资源前缀配置
##

## apisixResourceVersion apisix 资源版本，如需要蓝绿部署 apisix 多版本，配置此参数会变更 apisix 读取的 etcd key 前缀，以及 operator 选主的 key 前缀
##
apisixResourceVersion: ""
etcdPrefix: "/bk-gateway-"
etcdCertPath:
  apisix: /data/bkgateway/apisix-certs
  operator: /data/bkgateway/operator-certs

## 外部 ETCD 配置
##
externalEtcd:
  ## ETCD 配置
  default:
    ## 地址
    ##
    host: "etcd.example.com"
    ## 端口
    ##
    port: 2379
    ## 基础认证
    ##
    username: ""
    password: ""
    ## ETCD Client 证书配置
    ##
    tls:
      ## ETCD Client 证书 CA
      ##
      caBase64Encoded: ""
      ## ETCD Client 证书 Cert
      ##
      certBase64Encoded: ""
      ## ETCD Client 证书 KEY
      ##
      keyBase64Encoded: ""
      ## 已存在证书
      ##
      existingSecret: ""
      caCertFileName: "ca.crt"
      certFileName: "tls.crt"
      keyFileName: "tls.key"

## 内建 ETCD
##
etcd:
  ## 是否启用内建 ETCD，若禁用将启用使用 externalEtcd 配置
  enabled: true
  ## Do not override this, or you have to specify etcd option in `global` values
  nameOverride: etcd
  ## 镜像配置
  ##
  image:
    registry: docker.io
    repository: bitnami/etcd
    ## 镜像标签
    ##
    tag: 3.5.7-debian-11-r20

  ## 持久化存储
  ##
  persistence:
    enabled: true
    size: 10Gi
  ## 认证方式
  ##
  auth:
    ## RBAC 认证
    ##
    rbac:
      create: true
      allowNoneAuthentication: false
      rootPassword: "blueking"
    token:
      type: simple

  service:
    ports:
      client: 2379

## 密钥配置
##
keys:
  ## 应用 ID 及密钥
  ##
  apigatewayAppCode: "bk_apigateway"
  apigatewayAppSecret: ""
  ## 网关测试应用的应用 ID 及密钥，用于网关在线调试
  ##
  apigatewayTestAppCode: "bk_apigw_test"
  apigatewayTestAppSecret: ""
  ## 应用 bk_paas 的密钥
  ##
  paas2AppSecret: ""

## bkrepo 配置，用于将 SDK 上传到 paas 使用的 bkrepo 源中
##
bkrepoConfig:
  ## 是否启用
  ##
  enabled: true
  ## 源地址
  ##
  endpoint: "http://bkrepo.example.com"
  ## PaaS 项目
  ##
  bkpaas3Project: "bkpaas"
  ## PaaS 用户名
  ##
  bkpaas3Username: "bkpaas3"
  ## PaaS 密码
  ##
  bkpaas3Password: "blueking"
  ## 网关项目
  ##
  apigatewayProject: "bk_apigateway"
  ## 网关用户名
  ##
  apigatewayUsername: "bk_apigateway"
  ## 网关密码
  ##
  apigatewayPassword: "blueking"
  ## pypi 源名称
  ##
  pypiRepository: "pypi"
  ## generic bucket 名称
  ##
  genericBucket: "generic"

## 访问控制
##
rbac:
  ## 是否创建
  ##
  create: true

## 服务账户
##
serviceAccount:
  ## 是否创建
  ##
  create: true
  ## 名称
  ##
  name: ""

## 网关管理模块
dashboard:
  ## 是否启用
  ##
  enabled: true
  ## 副本数
  ##
  # replicaCount: 1
  ## 镜像配置
  ##
  image:
    ## 镜像源
    ##
    registry: hub.bktencent.com
    ## 镜像名称
    ##
    repository: "blueking/apigateway-dashboard"
    ## 镜像标签
    ##
    tag: "v1.12.14"
    ## 拉取策略
    ##
    pullPolicy: "IfNotPresent"
  ## ingress 配置
  ##
  ingress:
    ## 是否启用
    ##
    enabled: true
    ## 域名前缀
    ##
    name: "apigw"
    ## ingress 路径
    ##
    path: "/backend/"
    ## 注解
    ##
    annotations: {}
  ## 服务监控
  ##
  serviceMonitor:
    ## 是否启用
    ##
    enabled: false
    ## 采集间隔
    ##
    interval: "30s"
    ## 采集超时
    ##
    scrapeTimeout: "30s"
  ## 日志采集
  ##
  bkLogConfig:
    ## 是否启用
    ##
    enabled: false
    ## 标准输出数据 ID
    ##
    stdoutDataId: 1
    ## 标准输出编码
    ##
    stdoutEncoding: "utf-8"
  ## 蓝鲸调用链
  ##
  trace:
    serviceName: "bk-apigateway-dashboard"
    sampler: "always_on"
    instrument:
      celery: true
      dbApi: false
      redis: false
  ## 网关日志索引
  ##
  bkApigwApiLogEsIndex: "2_bklog_bkapigateway_apigateway_container*"
  ## ESB 日志索引
  ##
  bkEsbApiLogEsIndex: "2_bklog_bkapigateway_esb_container*"
  ## 网关 jwt issuer，不同网关实例可配置不同 issuer，以支持后端服务同时接入不同的网关实例，
  ## 此配置设置后，请谨慎修改，否则可能导致接入网关的后端服务解析 jwt 出错
  ##
  jwtIssuer: ""
  ## 内置网关信息，使用给定的信息更新对应的网关，如不存在，则进行创建
  ##
  builtinGateway:
    ## 请参考以下结构进行设置
    # "api-name":  # key 为网关名称，由小写字母、数字、连接符（-）组成
    #   appCode: "bk_app"  # 网关绑定的应用
    #   privateKeyBase64: ""  # 预设的私钥，以 base64 编码
    #   publicKeyBase64: ""  # 预设的公钥，以 base64 编码

  ## 资源配置
  ##
  resources:
    ## 资源限制
    ##
    limits:
      cpu: "2"
      memory: "2Gi"
    ## 资源请求
    ##
    requests:
      cpu: "100m"
      memory: "128Mi"
  ## 服务配置
  ##
  service:
    ## 服务类型
    ##
    type: "ClusterIP"
    ## 服务端口
    ##
    httpPort: 6000

  ## 额外环境变量
  ##
  extraEnvVars:
  # - name: "MY_ENV"
  #   value: "my_value"

  ## 额外卷挂载
  ##
  extraVolumeMounts:
  # - name: extras
  #   mountPath: /usr/share/extras
  #   readOnly: true

  ## 额外卷
  ##
  extraVolumes:
  # - name: extras
  #   secret:
  #     secretName: extra-secret

  ## 特性集开关
  ##
  featureFlags:
    ## 是否启用微网关
    ##
    microGatewayEnabled: false

## 网关管理前端
##
dashboardFe:
  ## 是否启用
  ##
  enabled: true
  ## 副本数
  ##
  # replicaCount: 1
  ## 镜像配置
  ##
  image:
    ## 镜像源
    ##
    registry: hub.bktencent.com
    ## 镜像名称
    ##
    repository: "blueking/apigateway-dashboard-fe"
    ## 镜像标签
    ##
    tag: "v1.12.14"
    ## 拉取策略
    ##
    pullPolicy: "IfNotPresent"
  ## ingress 配置，与 dashboard 共用 ingress 配置
  ##
  ingress:
    ## 是否启用
    ##
    enabled: true
    ## ingress 路径
    ##
    path: "/"

  ## 资源配置
  ##
  resources:
    ## 资源限制
    ##
    limits:
      cpu: "2"
      memory: "2Gi"
    ## 资源请求
    ##
    requests:
      cpu: "100m"
      memory: "128Mi"
  ## 服务配置
  ##
  service:
    ## 服务类型
    ##
    type: "ClusterIP"
    ## 服务端口
    ##
    httpPort: 6001

  ## 额外环境变量
  ##
  extraEnvVars:
  # - name: "MY_ENV"
  #   value: "my_value"

  ## 额外卷挂载
  ##
  extraVolumeMounts:
  # - name: extras
  #   mountPath: /usr/share/extras
  #   readOnly: true

  ## 额外卷
  ##
  extraVolumes:
  # - name: extras
  #   secret:
  #     secretName: extra-secret

## operator 模块
##
operator:
  ## 是否启用
  ##
  enabled: true
  ## 副本数，生产环境可调整为 3
  ##
  # replicaCount: 1
  ## 镜像配置
  ##
  image:
    ## 镜像源
    ##
    registry: hub.bktencent.com
    ## 镜像名称
    ##
    repository: "blueking/bk-micro-gateway-operator"
    ## 镜像标签
    ##
    tag: "v1.12.7"
    ## 拉取策略
    ##
    pullPolicy: "IfNotPresent"
  ## 服务监控
  ##
  serviceMonitor:
    ## 是否启用
    ##
    enabled: false
    ## 采集间隔
    ##
    interval: "30s"
    ## 采集超时
    ##
    scrapeTimeout: "30s"
  ## 日志采集
  ##
  bkLogConfig:
    ## 是否启用
    ##
    enabled: false
    ## 标准输出数据 ID
    ##
    stdoutDataId: 1
    ## 标准输出编码
    ##
    stdoutEncoding: "utf-8"
  ## 网关发布上报
  eventReporter:
    ## 版本探测配置
    versionProbe:
      ## 超时配置
      timeout: "2m"
      ## 探测缓冲区大小
      bufferSize: 300
      ## 探测重试配置
      retry:
        ## 重试总次数
        count: 60
        ## 每次探测间隔
        interval: "1000ms"
    ## 事件缓冲区大小
    eventBufferSize: 300
    ## 事件上报缓冲区大小
    reporterBufferSize: 100

  ## 网关发布
  publish:
    ## 网关资源发布写入etcd间隔
    etcdPutInterval: "100ms"

  ## trace
  trace:
    serviceName: "bk-apigateway-operator"
    sampler: "trace_id_ratio"
    samplerRatio: 0.1
  
  sentry:
    dsn: ""
    # err-2;dpanic-3;panic-4;fatal-5
    reportLogLevel: 3

  ## 资源配置
  ##
  resources:
    ## 资源限制
    ##
    limits:
      cpu: "2"
      memory: "2Gi"
    ## 资源请求
    ##
    requests:
      cpu: "100m"
      memory: "128Mi"
  ## 服务配置
  ##
  service:
    ## 服务类型
    ##
    type: "ClusterIP"
    ## healthz 端口
    ##
    httpPort: 6004
  ## 额外启动参数
  ##
  extraArgs:
    apisix-resource-flush-mode: diff
    apisix-resource-writer: etcd

  ## 额外环境变量
  ##
  extraEnvVars:
  # - name: "MY_ENV"
  #   value: "my_value"

  ## 额外卷挂载
  ##
  extraVolumeMounts:
  # - name: extra
  #   mountPath: /usr/share/extras
  #   readOnly: true

  ## 额外卷
  ##
  extraVolumes:
  # - name: extra
  #   secret:
  #     secretName: extra-secret

  ## 额外路由
  ##
  extraRoutes: []

  ## 存活探针配置
  livenessProbe:
    initialDelaySeconds: 20
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## 就绪探针配置
  readinessProbe:
    initialDelaySeconds: 20
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5

## 网关模块
##
apigateway:
  ## 是否启用
  ##
  enabled: true
  ## 副本数
  ##
  # replicaCount: 1
  ## 镜像配置
  ##
  image:
    ## 镜像源
    ##
    registry: hub.bktencent.com
    ## 镜像名称
    ##
    repository: "blueking/bk-micro-gateway-apisix"
    ## 镜像标签
    ##
    tag: "v1.12.9"
    ## 拉取策略
    ##
    pullPolicy: "IfNotPresent"
  ## ingress 配置
  ##
  ingress:
    ## 是否启用
    ##
    enabled: true
    ## 域名前缀
    ##
    name: "bkapi"
    ## ingress 路径
    ##
    path: "/"
    ## 注解
    ##
    annotations:
      # nginx.ingress.kubernetes.io/canary: "true"
      # nginx.ingress.kubernetes.io/canary-weight: "100"
      nginx.ingress.kubernetes.io/configuration-snippet: |
        proxy_set_header X-Request-Uri $request_uri;
      nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
  ## 服务监控
  ##
  serviceMonitor:
    ## 是否启用
    ##
    enabled: false
    ## 采集间隔
    ##
    interval: "30s"
    ## 采集超时
    ##
    scrapeTimeout: "30s"
  ## 日志采集
  ##
  bkLogConfig:
    ## 是否启用
    ##
    enabled: false
    ## 标准输出数据 ID
    ##
    stdoutDataId: 0
    ## 标准输出编码
    ##
    stdoutEncoding: "utf-8"
    ## 流水日志数据 ID
    ##
    containerDataId: 0
    ## 流水日志编码
    ##
    containerEncoding: "utf-8"
    ## 日志文件数据 ID
    ##
    fileDataId: 0
    ## 日志文件编码
    ##
    fileEncoding: "utf-8"
  ## 资源配置
  ##
  resources:
    ## 资源限制
    ##
    limits:
      cpu: "4"
      memory: "4Gi"
    ## 资源请求
    ##
    requests:
      cpu: "100m"
      memory: "128Mi"
  ## 服务配置
  ##
  service:
    ## 服务类型
    ##
    type: "ClusterIP"
    ## HTTP 服务端口
    ##
    httpPort: 6006
    ## HTTPS 服务端口
    ##
    httpsPort: 6007
    ## metric 服务端口
    ##
    metricPort: 6008
    ## control 服务端口
    ##
    controlPort: 6009
  ## Nginx 配置
  nginxConfig:
    httpClientMaxBodySize: "40m"
    workerProcesses: 4
    errorLogLevel: "warn"
  ## 额外 Nginx 配置
  ##
  extraNginxConfig:
    http_configuration_snippet: |
      client_body_buffer_size 64k;
      proxy_buffers 16 64k;
      proxy_buffer_size 64k;
      proxy_busy_buffers_size 128k;

  ## metric 配置
  ##
  metric:
    # disable all if you have 3000+ routes
    official:
      enable_latency: false
      enable_status: true
      enable_bandwidth: true
    default_buckets:
      - 50
      - 100
      - 500
      - 1000
      - 5000
      - 30000
      - 60000

  ## 调试配置
  ##
  apisixDebug:
    enabled: false
    # hookPhase:
    #   apisix:
    #     - http_access_phase
    #     - http_header_filter_phase
    #     - http_body_filter_phase
    #     - http_log_phase
    #   # add the plugin and phases you need
    #   apisix.plugins.bk-request-id:
    #     - rewrite
    #     - header_filter

  ## example for *Plugins: [], it's an array of plugins
  ## - bk-demo
  ## - bk-demo2
  ## 追加的 HTTP 插件
  ##
  appendPlugins: []
  ## 追加的 stream 插件
  ##
  appendStreamPlugins: []
  ## 覆盖的 HTTP 插件
  ##
  overridePlugins: []
  ## 覆盖的 stream 插件
  ##
  overrideStreamPlugins: []
  ## 追加的 plugin attrs
  ##
  appendPluginAttrs: {}
  ##
  apisixConfig:
    luaSharedDict: {}
    customLuaSharedDict: {}
    bkAuthAuthorizationKeys:
      - "app_code"
      - "app_secret"
      - "access_token"
      - "jwt"
      - "bk_app_code"
      - "bk_app_secret"
      - "bk_username"
      - "username"
      - "bk_token"
    bkAuthSensitiveKeys:
      - "access_token"
      - "app_secret"
      - "bk_app_secret"
      - "bk_token"
      - "bk_nonce"
      - "bk_timestamp"
      - "bk_signature"
    hosts: {}
  ## PluginMetadata 配置
  ##
  pluginMetadata:
    ## 应用并发限制
    ##
    bk-concurrency-limit:
      ## 阈值
      ##
      conn: 2000
      ## 容忍度
      ##
      burst: 1000
      ## 容忍延时
      ##
      defaultConnDelay: 1
    ## 客户端 IP 识别规则 http://nginx.org/en/docs/http/ngx_http_realip_module.html
    ##
    bk-real-ip:
      ## 是否递归查找 X-Forwarded-For 请求头
      ##
      recursive: true
      ## 信任 IP 的来源
      ##
      source: "http_x_forwarded_for"
      ## 信任的中间代理地址
      ##
      trustedAddresses:
        - "127.0.0.1"
        - "::1"
    ## OTEL 配置
    bk-opentelemetry:
      ## 采样方案
      ##
      sampler: parent_base
      ## 默认采样率
      ##
      samplerRatio: 0.001

  ## APISIX 管理密码
  ##
  adminApiKey: ""
  ## 额外 APISIX 配置
  ##
  extraApisixConfig: {}

  ## 额外环境变量
  ##
  extraEnvVars:
    ##  如果 BK_APIGW_NGINX_ERROR_LOG_SENTRY_DSN 非空，则会将 nginx 日志采集到 sentry
    ##
    - name: "BK_APIGW_NGINX_ERROR_LOG_SENTRY_DSN"
      value: ""

  # - name: "MY_ENV"
  #   value: "my_value"

  ## 额外卷挂载
  ##
  extraVolumeMounts:
  # - name: extras
  #   mountPath: /usr/share/extras
  #   readOnly: true

  ## 额外卷
  ##
  extraVolumes:
  # - name: extras
  #   secret:
  #     secretName: extra-secret

  ## 默认集群内 Service 名称，为空则不创建
  ##
  bkapiServiceName: "bk-apigateway-bkapi"

  ## 默认集群内 Service 类型
  ##
  bkapiServiceType: ClusterIP

  ## 额外服务端口
  ##
  extraServicePorts:
  # - name: extra
  #   port: 8888
  #   protocol: TCP
  #   targetPort: 8888

  ## 存活探针配置
  livenessProbe:
    enabled: true
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 8
  ## 就绪探针配置
  readinessProbe:
    enabled: true
    initialDelaySeconds: 60
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5

  ## 渐进式发布
  rollout:
    ## 是否启用
    ##
    enabled: false
    ## 失败策略，即检查任务失败后应该进行的动作，可选项：Backward, Rollback, Present
    ## - Backward: 回退到发布上一步骤暂停，直至手动介入
    ## - Rollback: 直接回滚，暂停原负载更新，销毁当前 canary，暂停，直至手动介入
    ## - Present: 暂停在当前步骤，直至手动介入
    ##
    failurePolicy: "Backward"
    ## 发布是否暂停，当手动介入后，需要继续发布时，将此字段修改为 "false"；如果需要暂停发布过程，将此字段修改为 "true"
    ##
    paused: "false"
    ## 指定发布过程，可以配置多个发布步骤，每个步骤可以配置三个参数：副本数、hook、暂停
    ##
    steps:
      - ## 灰度副本数，可以填百分比，也可以填具体数字
        ##
        replicas: 1
        ## 可以填写 duration 暂停指定时间，也可以填写为 "{}"，指定永久暂停直至手动介入
        ##
        pause:
          duration: 60s
      - replicas: 20%
        pause:
          duration: 60s
      - replicas: 50%
        pause:
          duration: 60s

## 网关 core service 模块
##
apigatewayCoreApi:
  ## 是否启用
  ##
  enabled: true

  debug: true
  logLevel: "debug"

  ## trace
  trace:
    serviceName: "bk-apigateway-core-api"
    sampler: "trace_id_ratio"
    samplerRatio: 0.001
    instrument:
      ginApi: true
      dbApi: true
  ## sentry
  sentry:
    dsn:
    # err-2;dpanic-3;panic-4;fatal-5
    reportLogLevel: 2
  ## 副本数
  ##
  # replicaCount: 1
  ## 镜像配置
  ##
  image:
    ## 镜像源
    ##
    registry: hub.bktencent.com
    ## 镜像名称
    ##
    repository: "blueking/bk-apigateway-core-api"
    ## 镜像标签
    ##
    tag: "v1.12.14"
    ## 拉取策略
    ##
    pullPolicy: "IfNotPresent"
  ## 服务监控
  ##
  serviceMonitor:
    ## 是否启用
    ##
    enabled: false
    ## 采集间隔
    ##
    interval: "30s"
    ## 采集超时
    ##
    scrapeTimeout: "30s"
  ## 日志采集
  ##
  bkLogConfig:
    ## 是否启用
    ##
    enabled: false
    ## 标准输出数据 ID
    ##
    stdoutDataId: 0
    ## 标准输出编码
    ##
    stdoutEncoding: "utf-8"
    ## 流水日志数据 ID
    ##
    containerDataId: 0
    ## 流水日志编码
    ##
    containerEncoding: "utf-8"
    ## 日志文件数据 ID
    ##
    fileDataId: 0
    ## 日志文件编码
    ##
    fileEncoding: "utf-8"
  ## 资源配置
  ##
  resources:
    ## 资源限制
    ##
    limits:
      cpu: "1"
      memory: "1Gi"
    ## 资源请求
    ##
    requests:
      cpu: "100m"
      memory: "128Mi"
  ## 服务配置
  ##
  service:
    ## 服务类型
    ##
    type: "NodePort"
    ## HTTP 服务端口
    ##
    httpPort: 5000

  ## 额外环境变量
  ##
  extraEnvVars:
  # - name: "MY_ENV"
  #   value: "my_value"

  ## 额外卷挂载
  ##
  extraVolumeMounts:
  # - name: extras
  #   mountPath: /usr/share/extras
  #   readOnly: true

  ## 额外卷
  ##
  extraVolumes:
  # - name: extras
  #   secret:
  #     secretName: extra-secret

  ## 存活探针配置
  livenessProbe:
    enabled: true
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5
  ## 就绪探针配置
  readinessProbe:
    enabled: true
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 5
    successThreshold: 1
    failureThreshold: 5

bkEsb:
  ## 是否启用
  ##
  enabled: true
  ## 副本数
  ##
  # replicaCount: 1
  ## 镜像配置
  ##
  image:
    ## 镜像源
    ##
    registry: hub.bktencent.com
    ## 镜像名称
    ##
    repository: "blueking/apigateway-esb"
    ## 镜像标签
    ##
    tag: "v1.12.14"
    ## 拉取策略
    ##
    pullPolicy: "IfNotPresent"
  ## ingress 配置，bkEsb 共用 apigateway 的 ingress 配置
  ##
  ingress:
    ## 是否启用
    ##
    enabled: true
    ## ingress 路径
    ##
    path: "/api/c/"
  ## 服务监控
  ##
  serviceMonitor:
    ## 是否启用
    ##
    enabled: false
    ## 采集间隔
    ##
    interval: "30s"
    ## 采集超时
    ##
    scrapeTimeout: "30s"
  ## 日志采集
  ##
  bkLogConfig:
    ## 是否启用
    ##
    enabled: false
    ## 标准输出数据 ID
    ##
    stdoutDataId: 1
    ## 标准输出编码
    ##
    stdoutEncoding: "utf-8"
    ## 日志文件数据 ID
    ##
    containerDataId: 1
    ## 日志文件编码
    ##
    containerEncoding: "utf-8"
    ## 请求流水日志文件目录
    ##
    logPath: "/app/logs/"
  ## 服务配置
  ##
  service:
    ## 服务类型
    ##
    type: "ClusterIP"
    ## 服务端口
    ##
    httpPort: 6010

  ## 资源配置
  ##
  resources:
    ## 资源限制
    ##
    limits:
      cpu: "4"
      memory: "4Gi"
    ## 资源请求
    ##
    requests:
      cpu: "100m"
      memory: "128Mi"

  ## esb 证书
  ##
  certs:
    ## GSE 系统证书
    ##
    - name: gseca.crt
      base64Encoded: ""
    - name: gse_esb_api_client.crt
      base64Encoded: ""
    - name: gse_esb_api_client.key
      base64Encoded: ""
    ## JOB 系统证书
    ##
    - name: job_esb_api_client.crt
      base64Encoded: ""
    - name: job_esb_api_client.key
      base64Encoded: ""

  ## esb 免用户认证应用白名单，填写 app code
  ##
  userVerificationExemptedApps: []

  ## 自定义组件 configmap 名称
  ##
  customComponentConfigMaps:
    # - configmap: "bk-esb-custom-component-configmap"
    #   mountPath: "my_component"

  ## 额外环境变量
  ##
  extraEnvVars:
  # - name: "MY_ENV"
  #   value: "my_value"

  ## 额外卷挂载
  ##
  extraVolumeMounts:
  # - name: extras
  #   mountPath: /usr/share/extras
  #   readOnly: true

  ## 额外卷
  ##
  extraVolumes:
  # - name: extras
  #   secret:
  #     secretName: extra-secret

k8sWaitFor:
  ## 镜像配置
  ##
  image:
    ## 镜像源
    ##
    registry: hub.bktencent.com
    ## 镜像名称
    ##
    repository: "groundnuty/k8s-wait-for"
    ## 镜像标签
    ##
    tag: "v1.5.1"
    ## 拉取策略
    ##
    pullPolicy: "IfNotPresent"

  ## 资源配置
  ##
  resources:
    ## 资源限制
    ##
    limits:
      cpu: "1"
      memory: "1Gi"
    ## 资源请求
    ##
    requests:
      cpu: "100m"
      memory: "128Mi"

## 蓝鲸 Login url（浏览器跳转登录用的 URL 前缀）
##
bkLoginUrl: "http://paas.example.com/login/"

## 蓝鲸登录后台的内部服务地址（一般用于校验登录 token）
##
bkLoginApiUrl: "http://bk-login-web"

## BKAuth API 地址
##
bkAuthApiUrl: "http://bkauth"

## 蓝鲸凭证管理后台 API 地址
##
bkSsmApiUrl: "http://bkssm-web"

## 蓝鲸文档中心 URL
##
bkDocsCenterUrl: "https://bk.tencent.com/docs"

## 蓝鲸文档中心后台 API 地址
##
bkDocsCenterApiUrl: "http://apps.example.com/bk--docs--center"

## 蓝鲸配置平台后台 API 地址
##
bkCmdbApiUrl: "http://bk-cmdb-api"

## 蓝鲸作业平台后台 API 访问地址
##
bkJobApiBackendUrl: "https://bk-job-gateway"

## 蓝鲸用户管理后台 API 地址
##
bkUserApiUrl: "http://bkuserapi-web"

## 蓝鲸权限中心 SaaS 的后台 API 地址
##
bkIamSaasApiUrl: "http://bkiam-saas-api"

## 蓝鲸 GSEKIT SaaS URL
##
bkGsekitUrl: "http://apps.example.com/bk--gsekit"

## 蓝鲸流程管理 SaaS URL
##
bkItsmUrl: "http://apps.example.com/bk--itsm"

## 蓝鲸标准运维 SaaS URL
##
bkSopsUrl: "http://apps.example.com/prod--api--bk--sops"

## 蓝鲸日志平台后台 API URL
##
bkLogSearchApiUrl: "http://bk-log-search-api"

## 蓝鲸监控平台后台 API URL
##
bkMonitorApiUrl: "http://bk-monitor-api"

## 蓝鲸节点管理 SaaS backend 模块 URL
##
bkNodemanApiUrl: "http://apps.example.com/prod--backend--bk--nodeman"

## 蓝鲸管控平台 cacheapi 模块 API 地址
##
bkGseCacheapiHost: "bk-gse-api"
bkGseCacheapiPort: "59313"

## 蓝鲸管控平台插件管理后台 API 地址
##
bkGsePmsUrl: "http://bk-gse-procmgr:52030"

## 蓝鲸管控平台配置后台 API 地址
##
bkGseConfigUrl: "http://bk-gse-config:59702"

## bk-data
##
bkDataUrl: "http://bk-data.example.com"

## bk-data-bksql
##
bkDataBksqlUrl: "http://bk-data-bksql.example.com"

## bk-data-processorapi
##
bkDataProcessorapiUrl: "http://bk-data-processorapi.example.com"

## bk-data-modelflow
##
bkDataModelflowUrl: "http://bk-data-modelflow.example.com"

## 计算平台后台服务 API 地址，默认是固定bkbase的namespace
##
bkDataV3AuthapiUrl: "http://bkbase-authapi-api.bkbase.svc.cluster.local:8000"
bkDataV3AccessapiUrl: "http://bkbase-datahubapi-api.bkbase.svc.cluster.local:8000"
bkDataV3DatabusapiUrl: "http://bkbase-datahubapi-api.bkbase.svc.cluster.local:8000"
bkDataV3DataflowapiUrl: "http://bkbase-dataflowapi-api.bkbase.svc.cluster.local:8000"
bkDataV3DatamanageapiUrl: "http://bkbase-datamanageapi-api.bkbase.svc.cluster.local:8000"
bkDataV3DataqueryapiUrl: "http://bkbase-queryengine-api.bkbase.svc.cluster.local:8000"
bkDataV3MetaapiUrl: "http://bkbase-metaapi-api.bkbase.svc.cluster.local:8000"
bkDataV3StorekitapiUrl: "http://bkbase-datahubapi-api.bkbase.svc.cluster.local:8000"
bkDataV3BksqlUrl: "http://bkbase-bksqlextend-service.bkbase.svc.cluster.local:8596"
bkDataV3ModelapiUrl: ""
bkDataV3DatacubeapiUrl: ""
bkDataV3AlgorithmapiUrl: ""
bkDataV3DatalabapiUrl: "http://bkbase-datalabapi-api.bkbase.svc.cluster.local:8000"
bkDataV3AiopsapiUrl: "http://bkbase-aiopsapi-api.bkbase.svc.cluster.local:8000"
bkDataV3ResourcecenterapiUrl: "http://bkbase-resourcecenterapi-api.bkbase.svc.cluster.local:8000"
bkDataV3QueryengineapiUrl: "http://bkbase-queryengine-api.bkbase.svc.cluster.local:8000"
bkDataV3LangserverUrl: "http://bkbase-bksql-language-server.bkbase.svc.cluster.local:8000"

## fta
##
bkFtaUrl: "http://bk-fta.example.com"

## devops (旧版)
##
bkDevopsUrl: "http://bk-devops.example.com"

## cicdkit
##
bkCicdkitUrl: "http://bk-cicdkit.example.com"

## bscp
##
bkBscpApiUrl: "http://bk-bscp-api.example.com"

## apigw-dashboard 接口地址，未设置时，默认使用网关管理模块 dashboard 的服务地址
##
bkApigwDashboardApiUrl: ""

## bk-paas3 管理端地址，未设置时，根据 bkDomain 自动生成
##
bkPaas3Url: ""

## 额外资源
##
additionalResources: []

## 额外 initContainers
##
initContainers: []
## 全局密钥
#  ##
encrypt:
  crypto:
    key: "G7kR9mT2sVbXfWqP"
    mark: "ENC"

