#!/bin/bash


BK_ENV=production PAAS_LOGGING_DIR=/data/bkee/logs/open_paas python manage.py shell --plain << EOF
import sys; sys.ps1 = sys.ps2 = ""; print("")

from django.conf import settings


# need delete legacy settings keys: PAAS_HOST, HOST_BK_LOGIN, HOST_CC
KEYS_IN_LEGACY_ESB_SETTINGS = """
HOST_CC_V3
HOST_JOB
GSE_CACHEAPI_HOST
GSE_CACHEAPI_PORT
GSE_PMS_HOST
BK_GSE_CONFIG_ADDR
HOST_DATA
DATA_BKSQL_HOST
DATA_PROCESSORAPI_HOST
DATA_MODELFLOW_HOST
DATAV3_AUTHAPI_HOST
DATAV3_ACCESSAPI_HOST
DATAV3_DATABUSAPI_HOST
DATAV3_DATAFLOWAPI_HOST
DATAV3_DATAMANAGEAPI_HOST
DATAV3_DATAQUERYAPI_HOST
DATAV3_METAAPI_HOST
DATAV3_STOREKITAPI_HOST
DATAV3_BKSQL_HOST
DATAV3_MODELAPI_HOST
DATAV3_DATACUBEAPI_HOST
DATAV3_ALGORITHMAPI_HOST
DATAV3_DATALABAPI_HOST
DATAV3_AIOPSAPI_HOST
HOST_FTA
DEVOPS_HOST
CICDKIT_HOST
MONITOR_HOST
MONITOR_V3_HOST
USERMGR_HOST
BK_LOG_HOST
NODEMAN_HOST
BK_BSCP_API_ADDR
"""


BK_ESB_SETTINGS_KEY_TO_CHART_VAR_MAP = {
    "HOST_CC_V3": "bkCmdbApiUrl",
    "HOST_JOB": "bkJobApiBackendUrl",
    "GSE_CACHEAPI_HOST": "bkGseCacheapiHost",
    "GSE_CACHEAPI_PORT": "bkGseCacheapiPort",
    "GSE_PMS_HOST": "bkGsePmsUrl",
    "BK_GSE_CONFIG_ADDR": "bkGseConfigUrl",
    "HOST_DATA": "bkDataUrl",
    "DATA_BKSQL_HOST": "bkDataBksqlUrl",
    "DATA_PROCESSORAPI_HOST": "bkDataProcessorapiUrl",
    "DATA_MODELFLOW_HOST": "bkDataModelflowUrl",
    "DATAV3_AUTHAPI_HOST": "bkDataV3AuthapiUrl",
    "DATAV3_ACCESSAPI_HOST": "bkDataV3AccessapiUrl",
    "DATAV3_DATABUSAPI_HOST": "bkDataV3DatabusapiUrl",
    "DATAV3_DATAFLOWAPI_HOST": "bkDataV3DataflowapiUrl",
    "DATAV3_DATAMANAGEAPI_HOST": "bkDataV3DatamanageapiUrl",
    "DATAV3_DATAQUERYAPI_HOST": "bkDataV3DataqueryapiUrl",
    "DATAV3_METAAPI_HOST": "bkDataV3MetaapiUrl",
    "DATAV3_STOREKITAPI_HOST": "bkDataV3StorekitapiUrl",
    "DATAV3_BKSQL_HOST": "bkDataV3BksqlUrl",
    "DATAV3_MODELAPI_HOST": "bkDataV3ModelapiUrl",
    "DATAV3_DATACUBEAPI_HOST": "bkDataV3DatacubeapiUrl",
    "DATAV3_ALGORITHMAPI_HOST": "bkDataV3AlgorithmapiUrl",
    "DATAV3_DATALABAPI_HOST": "bkDataV3DatalabapiUrl",
    "DATAV3_AIOPSAPI_HOST": "bkDataV3AiopsapiUrl",
    "HOST_FTA": "bkFtaUrl",
    "DEVOPS_HOST": "bkDevopsUrl",
    "CICDKIT_HOST": "bkCicdkitUrl",
    "MONITOR_HOST": "bkMonitorUrl",
    "MONITOR_V3_HOST": "bkMonitorApiUrl",
    "USERMGR_HOST": "bkUserApiUrl",
    "BK_LOG_HOST": "bkLogSearchApiUrl",
    "NODEMAN_HOST": "bkNodemanApiUrl",
    "BK_BSCP_API_ADDR": "bkBscpApiUrl",
}


def generate_esb_external_dependencies_conf():
    conf = []
    for legacy_settings_key in KEYS_IN_LEGACY_ESB_SETTINGS.split():
        chart_var_name = BK_ESB_SETTINGS_KEY_TO_CHART_VAR_MAP.pop(legacy_settings_key, None)
        if not chart_var_name:
            print("key %s not exist in bk-esb settings" % legacy_settings_key)
            continue
        legacy_settings_value = getattr(settings, legacy_settings_key, None)
        if legacy_settings_value not in (None, "", ":"):
            conf.append('%s: "%s"' % (chart_var_name, legacy_settings_value))
    return conf


conf = generate_esb_external_dependencies_conf()
print("\n".join(conf))

EOF
