{{- if .Values.rbac.create }}
apiVersion: {{ include "common.capabilities.rbac.apiVersion" . }}
kind: Role
metadata:
  name: {{ template "common.names.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
rules:
  {{- $pspAvailable := (semverCompare "<1.25-0" (include "common.capabilities.kubeVersion" .)) -}}
  {{- if and $pspAvailable .Values.podSecurityPolicy.enabled }}
  - apiGroups:
      - '{{ template "podSecurityPolicy.apiGroup" . }}'
    resources:
      - 'podsecuritypolicies'
    verbs:
      - 'use'
    resourceNames: [{{ printf "%s-master" (include "common.names.fullname" .) }}]
  {{- end }}
  {{- if .Values.rbac.rules }}
  {{- include "common.tplvalues.render" ( dict "value" .Values.rbac.rules "context" $ ) | nindent 2 }}
  {{- end }}
{{- end }}
