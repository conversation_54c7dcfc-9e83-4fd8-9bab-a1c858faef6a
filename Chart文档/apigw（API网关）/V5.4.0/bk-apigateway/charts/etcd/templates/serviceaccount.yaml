{{- if .Values.serviceAccount.create }}
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: {{ .Values.serviceAccount.automountServiceAccountToken }}
metadata:
  name: {{ include "etcd.serviceAccountName" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.serviceAccount.labels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.serviceAccount.labels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if or .Values.commonAnnotations .Values.serviceAccount.annotations }}
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.serviceAccount.annotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.serviceAccount.annotations "context" $ ) | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}
