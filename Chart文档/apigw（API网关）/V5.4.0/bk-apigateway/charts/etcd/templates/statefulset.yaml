apiVersion: {{ include "common.capabilities.statefulset.apiVersion" . }}
kind: StatefulSet
metadata:
  name: {{ include "common.names.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels: {{- include "common.labels.matchLabels" . | nindent 6 }}
  serviceName: {{ printf "%s-headless" (include "common.names.fullname" .) | trunc 63 | trimSuffix "-" }}
  podManagementPolicy: {{ .Values.podManagementPolicy }}
  updateStrategy: {{- include "common.tplvalues.render" (dict "value" .Values.updateStrategy "context" $ ) | nindent 4 }}
  template:
    metadata:
      labels: {{- include "common.labels.standard" . | nindent 8 }}
        {{- if .Values.podLabels }}
        {{- include "common.tplvalues.render" (dict "value" .Values.podLabels "context" $) | nindent 8 }}
        {{- end }}
      annotations:
        {{- if .Values.podAnnotations }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.podAnnotations "context" $) | nindent 8 }}
        {{- end }}
        {{- if and .Values.metrics.enabled .Values.metrics.podAnnotations }}
        {{- include "common.tplvalues.render" ( dict "value" .Values.metrics.podAnnotations "context" $) | nindent 8 }}
        {{- end }}
        {{- if (include "etcd.createConfigmap" .) }}
        checksum/configuration: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        {{- end }}
        {{- if (include "etcd.token.createSecret" .) }}
        checksum/token-secret: {{ include (print $.Template.BasePath "/token-secrets.yaml") . | sha256sum }}
        {{- end }}
    spec:
      {{- include "etcd.imagePullSecrets" . | nindent 6 }}
      {{- if .Values.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.podAffinityPreset "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.podAntiAffinityPreset "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.nodeAffinityPreset.type "key" .Values.nodeAffinityPreset.key "values" .Values.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      {{- if .Values.nodeSelector }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.nodeSelector "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.tolerations "context" $) | nindent 8 }}
      {{- end }}
      {{- if .Values.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      {{- end }}
      {{- if .Values.schedulerName }}
      schedulerName: {{ .Values.schedulerName }}
      {{- end }}
      {{- if .Values.topologySpreadConstraints }}
      topologySpreadConstraints: {{- include "common.tplvalues.render" (dict "value" .Values.topologySpreadConstraints "context" .) | nindent 8 }}
      {{- end }}
      {{- if .Values.priorityClassName }}
      priorityClassName: {{ .Values.priorityClassName }}
      {{- end }}
      {{- if .Values.runtimeClassName }}
      runtimeClassName: {{ .Values.runtimeClassName }}
      {{- end }}
      {{- if .Values.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      {{- if .Values.shareProcessNamespace }}
      shareProcessNamespace: {{ .Values.shareProcessNamespace }}
      {{- end }}
      serviceAccountName: {{ include "etcd.serviceAccountName" $ | quote }}
      {{- if or .Values.initContainers (and .Values.volumePermissions.enabled .Values.persistence.enabled) }}
      initContainers:
        {{- if .Values.initContainers }}
        {{- include "common.tplvalues.render" (dict "value" .Values.initContainers "context" $) | nindent 8 }}
        {{- end }}
        {{- if and .Values.volumePermissions.enabled .Values.persistence.enabled }}
        - name: volume-permissions
          image: {{ include "etcd.volumePermissions.image" . }}
          imagePullPolicy: {{ .Values.volumePermissions.image.pullPolicy | quote }}
          command:
            - /bin/bash
            - -ec
            - |
              chown -R {{ .Values.containerSecurityContext.runAsUser }}:{{ .Values.podSecurityContext.fsGroup }} /bitnami/etcd
          securityContext:
            runAsUser: 0
          {{- if .Values.volumePermissions.resources }}
          resources: {{- include "common.tplvalues.render" (dict "value" .Values.volumePermissions.resources "context" $) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: data
              mountPath: /bitnami/etcd
        {{- end }}
      {{- end }}
      containers:
        {{- $replicaCount := int .Values.replicaCount }}
        {{- $peerPort := int .Values.containerPorts.peer }}
        {{- $etcdFullname := include "common.names.fullname" . }}
        {{- $releaseNamespace := .Release.Namespace }}
        {{- $etcdHeadlessServiceName := (printf "%s-%s" $etcdFullname "headless" | trunc 63 | trimSuffix "-") }}
        {{- $clusterDomain := .Values.clusterDomain }}
        {{- $etcdPeerProtocol := include "etcd.peerProtocol" . }}
        {{- $etcdClientProtocol := include "etcd.clientProtocol" . }}
        - name: etcd
          image: {{ include "etcd.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          {{- if .Values.containerSecurityContext.enabled }}
          securityContext: {{- omit .Values.containerSecurityContext "enabled" | toYaml | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.command "context" $) | nindent 12 }}
          {{- else if .Values.command }}
          command: {{- include "common.tplvalues.render" (dict "value" .Values.command "context" $) | nindent 12 }}
          {{- end }}
          {{- if .Values.diagnosticMode.enabled }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.diagnosticMode.args "context" $) | nindent 12 }}
          {{- else if .Values.args }}
          args: {{- include "common.tplvalues.render" (dict "value" .Values.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            - name: BITNAMI_DEBUG
              value: {{ ternary "true" "false" (or .Values.image.debug .Values.diagnosticMode.enabled) | quote }}
            - name: MY_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: MY_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: MY_STS_NAME
              value: {{ include "common.names.fullname" . | quote }}
            - name: ETCDCTL_API
              value: "3"
            - name: ETCD_ON_K8S
              value: "yes"
            - name: ETCD_START_FROM_SNAPSHOT
              value: {{ ternary "yes" "no" .Values.startFromSnapshot.enabled | quote }}
            - name: ETCD_DISASTER_RECOVERY
              value: {{ ternary "yes" "no" .Values.disasterRecovery.enabled | quote }}
            - name: ETCD_NAME
              value: "$(MY_POD_NAME)"
            - name: ETCD_DATA_DIR
              value: "/bitnami/etcd/data"
            - name: ETCD_LOG_LEVEL
              value: {{ ternary "debug" .Values.logLevel .Values.image.debug | quote }}
            - name: ALLOW_NONE_AUTHENTICATION
              value: {{ ternary "yes" "no" (and (not (or .Values.auth.rbac.create .Values.auth.rbac.enabled)) .Values.auth.rbac.allowNoneAuthentication) | quote }}
            {{- if or .Values.auth.rbac.create .Values.auth.rbac.enabled }}
            - name: ETCD_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "etcd.secretName" . }}
                  key: {{ include "etcd.secretPasswordKey" . }}
            {{- end }}
            {{- if .Values.auth.token.enabled }}
            - name: ETCD_AUTH_TOKEN
              {{- if eq .Values.auth.token.type "jwt" }}
              value: {{ printf "jwt,priv-key=/opt/bitnami/etcd/certs/token/%s,sign-method=%s,ttl=%s" .Values.auth.token.privateKey.filename .Values.auth.token.signMethod .Values.auth.token.ttl | quote }}
              {{- else if eq .Values.auth.token.type "simple" }}
              value: "simple"
              {{- end }}
            {{- end }}
            - name: ETCD_ADVERTISE_CLIENT_URLS
              value: "{{ $etcdClientProtocol }}://$(MY_POD_NAME).{{ $etcdHeadlessServiceName }}.{{ .Release.Namespace }}.svc.{{ $clusterDomain }}:{{ .Values.containerPorts.client }},{{ $etcdClientProtocol }}://{{ $etcdFullname }}.{{ .Release.Namespace }}.svc.{{ $clusterDomain }}:{{ coalesce .Values.service.ports.client .Values.service.port }}"
            - name: ETCD_LISTEN_CLIENT_URLS
              value: "{{ $etcdClientProtocol }}://0.0.0.0:{{ .Values.containerPorts.client }}"
            - name: ETCD_INITIAL_ADVERTISE_PEER_URLS
              value: "{{ $etcdPeerProtocol }}://$(MY_POD_NAME).{{ $etcdHeadlessServiceName }}.{{ .Release.Namespace }}.svc.{{ $clusterDomain }}:{{ .Values.containerPorts.peer }}"
            - name: ETCD_LISTEN_PEER_URLS
              value: "{{ $etcdPeerProtocol }}://0.0.0.0:{{ .Values.containerPorts.peer }}"
            {{- if .Values.autoCompactionMode }}
            - name: ETCD_AUTO_COMPACTION_MODE
              value: {{ .Values.autoCompactionMode | quote }}
            {{- end }}
            {{- if .Values.autoCompactionRetention }}
            - name: ETCD_AUTO_COMPACTION_RETENTION
              value: {{ .Values.autoCompactionRetention | quote }}
            {{- end }}
            {{- if .Values.maxProcs }}
            - name: GOMAXPROCS
              value: {{ .Values.maxProcs }}
            {{- end }}
            {{- if gt $replicaCount 1 }}
            - name: ETCD_INITIAL_CLUSTER_TOKEN
              value: "etcd-cluster-k8s"
            - name: ETCD_INITIAL_CLUSTER_STATE
              value: {{ default (ternary "new" "existing" .Release.IsInstall) .Values.initialClusterState | quote }}
            {{- $initialCluster := list }}
            {{- range $e, $i := until $replicaCount }}
            {{- $initialCluster = append $initialCluster (printf "%s-%d=%s://%s-%d.%s.%s.svc.%s:%d" $etcdFullname $i $etcdPeerProtocol $etcdFullname $i $etcdHeadlessServiceName $releaseNamespace $clusterDomain $peerPort) }}
            {{- end }}
            - name: ETCD_INITIAL_CLUSTER
              value: {{ join "," $initialCluster | quote }}
            {{- end }}
            - name: ETCD_CLUSTER_DOMAIN
              value: {{ printf "%s.%s.svc.%s" $etcdHeadlessServiceName $releaseNamespace $clusterDomain | quote }}
            {{- if and .Values.auth.client.secureTransport .Values.auth.client.useAutoTLS }}
            - name: ETCD_AUTO_TLS
              value: "true"
            {{- else if .Values.auth.client.secureTransport }}
            - name: ETCD_CERT_FILE
              value: "/opt/bitnami/etcd/certs/client/{{ .Values.auth.client.certFilename }}"
            - name: ETCD_KEY_FILE
              value: "/opt/bitnami/etcd/certs/client/{{ .Values.auth.client.certKeyFilename }}"
            {{- if .Values.auth.client.enableAuthentication }}
            - name: ETCD_CLIENT_CERT_AUTH
              value: "true"
            - name: ETCD_TRUSTED_CA_FILE
              value: "/opt/bitnami/etcd/certs/client/{{ .Values.auth.client.caFilename | default "ca.crt" }}"
            {{- else if .Values.auth.client.caFilename }}
            - name: ETCD_TRUSTED_CA_FILE
              value: "/opt/bitnami/etcd/certs/client/{{ .Values.auth.client.caFilename | default "ca.crt" }}"
            {{- end }}
            {{- end }}
            {{- if and .Values.auth.peer.secureTransport .Values.auth.peer.useAutoTLS }}
            - name: ETCD_PEER_AUTO_TLS
              value: "true"
            {{- else if .Values.auth.peer.secureTransport }}
            - name: ETCD_PEER_CERT_FILE
              value: "/opt/bitnami/etcd/certs/peer/{{ .Values.auth.peer.certFilename }}"
            - name: ETCD_PEER_KEY_FILE
              value: "/opt/bitnami/etcd/certs/peer/{{ .Values.auth.peer.certKeyFilename }}"
            {{- if .Values.auth.peer.enableAuthentication }}
            - name: ETCD_PEER_CLIENT_CERT_AUTH
              value: "true"
            - name: ETCD_PEER_TRUSTED_CA_FILE
              value: "/opt/bitnami/etcd/certs/peer/{{ .Values.auth.peer.caFilename | default "ca.crt" }}"
            {{- else if .Values.auth.peer.caFilename }}
            - name: ETCD_PEER_TRUSTED_CA_FILE
              value: "/opt/bitnami/etcd/certs/peer/{{ .Values.auth.peer.caFilename | default "ca.crt" }}"
            {{- end }}
            {{- end }}
            {{- if .Values.startFromSnapshot.enabled }}
            - name: ETCD_INIT_SNAPSHOT_FILENAME
              value: {{ .Values.startFromSnapshot.snapshotFilename | quote }}
            - name: ETCD_INIT_SNAPSHOTS_DIR
              value: {{ ternary "/snapshots" "/init-snapshot" (and .Values.disasterRecovery.enabled (not .Values.disasterRecovery.pvc.existingClaim)) | quote }}
            {{- end }}
            {{- if .Values.extraEnvVars }}
            {{- include "common.tplvalues.render" (dict "value" .Values.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.extraEnvVarsCM }}
            - configMapRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.extraEnvVarsCM "context" $) }}
            {{- end }}
            {{- if .Values.extraEnvVarsSecret }}
            - secretRef:
                name: {{ include "common.tplvalues.render" (dict "value" .Values.extraEnvVarsSecret "context" $) }}
            {{- end }}
          ports:
            - name: client
              containerPort: {{ .Values.containerPorts.client }}
              protocol: TCP
            - name: peer
              containerPort: {{ .Values.containerPorts.peer }}
              protocol: TCP
          {{- if not .Values.diagnosticMode.enabled }}
          {{- if .Values.customLivenessProbe }}
          livenessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customLivenessProbe "context" $) | nindent 12 }}
          {{- else if .Values.livenessProbe.enabled }}
          livenessProbe:
            exec:
              command:
                - /opt/bitnami/scripts/etcd/healthcheck.sh
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.livenessProbe.successThreshold }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.customReadinessProbe }}
          readinessProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customReadinessProbe "context" $) | nindent 12 }}
          {{- else if .Values.readinessProbe.enabled }}
          readinessProbe:
            exec:
              command:
                - /opt/bitnami/scripts/etcd/healthcheck.sh
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            successThreshold: {{ .Values.readinessProbe.successThreshold }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.customStartupProbe }}
          startupProbe: {{- include "common.tplvalues.render" (dict "value" .Values.customStartupProbe "context" $) | nindent 12 }}
          {{- else if .Values.startupProbe.enabled }}
          startupProbe:
            exec:
              command:
                - /opt/bitnami/scripts/etcd/healthcheck.sh
            initialDelaySeconds: {{ .Values.startupProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.startupProbe.periodSeconds }}
            timeoutSeconds: {{ .Values.startupProbe.timeoutSeconds }}
            successThreshold: {{ .Values.startupProbe.successThreshold }}
            failureThreshold: {{ .Values.startupProbe.failureThreshold }}
          {{- end }}
          {{- if .Values.lifecycleHooks }}
          lifecycle: {{- include "common.tplvalues.render" (dict "value" .Values.lifecycleHooks "context" $) | nindent 12 }}
          {{- else if and (gt $replicaCount 1) .Values.removeMemberOnContainerTermination }}
          lifecycle:
            preStop:
              exec:
                command:
                  - /opt/bitnami/scripts/etcd/prestop.sh
          {{- end }}
          {{- end }}
          {{- if .Values.resources }}
          resources: {{- include "common.tplvalues.render" (dict "value" .Values.resources "context" $) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - name: data
              mountPath: /bitnami/etcd
            {{- if and (eq .Values.auth.token.enabled true) (eq .Values.auth.token.type "jwt") }}
            - name: etcd-jwt-token
              mountPath: /opt/bitnami/etcd/certs/token/
              readOnly: true
            {{- end }}
            {{- if or (and .Values.startFromSnapshot.enabled (not .Values.disasterRecovery.enabled)) (and .Values.disasterRecovery.enabled .Values.startFromSnapshot.enabled .Values.disasterRecovery.pvc.existingClaim) }}
            - name: init-snapshot-volume
              mountPath: /init-snapshot
            {{- end }}
            {{- if or .Values.disasterRecovery.enabled (and .Values.disasterRecovery.enabled .Values.startFromSnapshot.enabled) }}
            - name: snapshot-volume
              mountPath: /snapshots
            {{- end }}
            {{- if or .Values.configuration .Values.existingConfigmap }}
            - name: etcd-config
              mountPath: /opt/bitnami/etcd/conf/
            {{- end }}
            {{- if or .Values.auth.client.enableAuthentication (and .Values.auth.client.secureTransport (not .Values.auth.client.useAutoTLS )) }}
            - name: etcd-client-certs
              mountPath: /opt/bitnami/etcd/certs/client/
              readOnly: true
            {{- end }}
            {{- if or .Values.auth.peer.enableAuthentication (and .Values.auth.peer.secureTransport (not .Values.auth.peer.useAutoTLS )) }}
            - name: etcd-peer-certs
              mountPath: /opt/bitnami/etcd/certs/peer/
              readOnly: true
            {{- end }}
            {{- if .Values.extraVolumeMounts }}
            {{- include "common.tplvalues.render" (dict "value" .Values.extraVolumeMounts "context" $) | nindent 12 }}
            {{- end }}
        {{- if .Values.sidecars }}
        {{- include "common.tplvalues.render" (dict "value" .Values.sidecars "context" $) | nindent 8 }}
        {{- end }}
      volumes:
        {{- if and (eq .Values.auth.token.enabled true) (eq .Values.auth.token.type "jwt") }}
        - name: etcd-jwt-token
          secret:
            secretName: {{ include "etcd.token.secretName" . }}
            defaultMode: 256
        {{- end }}
        {{- if or (and .Values.startFromSnapshot.enabled (not .Values.disasterRecovery.enabled)) (and .Values.disasterRecovery.enabled .Values.startFromSnapshot.enabled .Values.disasterRecovery.pvc.existingClaim) }}
        - name: init-snapshot-volume
          persistentVolumeClaim:
            claimName: {{ .Values.startFromSnapshot.existingClaim }}
        {{- end }}
        {{- if or .Values.disasterRecovery.enabled (and .Values.disasterRecovery.enabled .Values.startFromSnapshot.enabled) }}
        - name: snapshot-volume
          persistentVolumeClaim:
            claimName: {{ include "etcd.disasterRecovery.pvc.name" . }}
        {{- end }}
        {{- if or .Values.configuration .Values.existingConfigmap }}
        - name: etcd-config
          configMap:
            name: {{ include "etcd.configmapName" . }}
        {{- end }}
        {{- if or .Values.auth.client.enableAuthentication (and .Values.auth.client.secureTransport (not .Values.auth.client.useAutoTLS )) }}
        - name: etcd-client-certs
          secret:
            secretName: {{ required "A secret containing the client certificates is required" (tpl .Values.auth.client.existingSecret .) }}
            defaultMode: 256
        {{- end }}
        {{- if or .Values.auth.peer.enableAuthentication (and .Values.auth.peer.secureTransport (not .Values.auth.peer.useAutoTLS )) }}
        - name: etcd-peer-certs
          secret:
            secretName: {{ required "A secret containing the peer certificates is required" (tpl .Values.auth.peer.existingSecret .) }}
            defaultMode: 256
        {{- end }}
        {{- if .Values.extraVolumes }}
        {{- include "common.tplvalues.render" (dict "value" .Values.extraVolumes "context" $) | nindent 8 }}
        {{- end }}
  {{- if not .Values.persistence.enabled }}
        - name: data
          emptyDir: {}
  {{- else }}
  {{- if .Values.persistentVolumeClaimRetentionPolicy.enabled }}
  persistentVolumeClaimRetentionPolicy:
    whenDeleted: {{ .Values.persistentVolumeClaimRetentionPolicy.whenDeleted }}
    whenScaled: {{ .Values.persistentVolumeClaimRetentionPolicy.whenScaled }}
  {{- end }}
  volumeClaimTemplates:
    - metadata:
        name: data
        {{- if .Values.persistence.annotations }}
        annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.persistence.annotations "context" $) | nindent 10 }}
        {{- end }}
        {{- if .Values.persistence.labels }}
        labels: {{- include "common.tplvalues.render" ( dict "value" .Values.persistence.labels "context" $) | nindent 10 }}
        {{- end }}
      spec:
        accessModes:
        {{- range .Values.persistence.accessModes }}
          - {{ . | quote }}
        {{- end }}
        resources:
          requests:
            storage: {{ .Values.persistence.size | quote }}
        {{- if .Values.persistence.selector }}
        selector: {{- include "common.tplvalues.render" ( dict "value" .Values.persistence.selector "context" $) | nindent 10 }}
        {{- end }}
        {{ include "common.storage.class" (dict "persistence" .Values.persistence "global" .Values.global) }}
  {{- end }}
