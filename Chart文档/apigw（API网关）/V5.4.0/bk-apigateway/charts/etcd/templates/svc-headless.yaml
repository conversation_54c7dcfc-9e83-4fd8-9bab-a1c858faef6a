apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-headless" (include "common.names.fullname" .) | trunc 63 | trimSuffix "-" }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    service.alpha.kubernetes.io/tolerate-unready-endpoints: "true"
    {{- if .Values.service.headless.annotations }}
    {{- include "common.tplvalues.render" (dict "value" .Values.service.headless.annotations "context" $) | nindent 4 }}
    {{- end }}
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" $) | nindent 4 }}
    {{- end }}
spec:
  type: ClusterIP
  clusterIP: None
  publishNotReadyAddresses: true
  ports:
    {{- if .Values.service.clientPortNameOverride }}
    {{- if .Values.auth.client.secureTransport }}
    - name: {{ .Values.service.clientPortNameOverride }}-ssl
    {{- else }}
    - name: {{ .Values.service.clientPortNameOverride }}
    {{- end }}
    {{- else }}
    - name: client
    {{- end }}
      port: {{ .Values.containerPorts.client }}
      targetPort: client
    {{- if .Values.service.peerPortNameOverride }}
    {{- if .Values.auth.peer.secureTransport }}
    - name: {{ .Values.service.peerPortNameOverride }}-ssl
    {{- else }}
    - name: {{ .Values.service.peerPortNameOverride }}
    {{- end }}
    {{- else }}
    - name: peer
    {{- end }}
      port: {{ .Values.containerPorts.peer }}
      targetPort: peer
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
