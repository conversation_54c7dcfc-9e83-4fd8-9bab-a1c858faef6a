{{- if not (.Capabilities.APIVersions.Has "apiextensions.k8s.io/v1") -}}

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.0
    helm.sh/resource-policy: keep
  creationTimestamp: null
  name: bkgatewayendpoints.gateway.bk.tencent.com
spec:
  group: gateway.bk.tencent.com
  names:
    kind: BkGatewayEndpoints
    listKind: BkGatewayEndpointsList
    plural: bkgatewayendpoints
    singular: bkgatewayendpoints
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      description: BkGatewayEndpoints is the Schema for the BkGatewayEndpoints API
      properties:
        apiVersion:
          description: 'APIVersion defines the versioned schema of this representation
            of an object. Servers should convert recognized schemas to the latest
            internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
          type: string
        kind:
          description: 'Kind is a string value representing the REST resource this
            object represents. Servers may infer this from the endpoint the client
            submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
          type: string
        metadata:
          type: object
        spec:
          description: BkGatewayEndpoints defines the desired state of BkGatewayEndpoints
          properties:
            nodes:
              items:
                description: BkGatewayNode node of upstream
                properties:
                  host:
                    type: string
                  port:
                    type: integer
                  priority:
                    type: integer
                  weight:
                    type: integer
                type: object
              type: array
          type: object
        status:
          description: BkGatewayEndpointsStatus defines the observed state of BkGatewayEndpoints
          properties:
            message:
              description: Message message for bk gateway stage
              type: string
            status:
              description: Status status for bk gateway stage
              type: string
          type: object
      type: object
  version: v1beta1
  versions:
  - name: v1beta1
    served: true
    storage: true
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
{{- end }}
