{{- if not (.Capabilities.APIVersions.Has "apiextensions.k8s.io/v1") -}}

---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.0
    helm.sh/resource-policy: keep
  creationTimestamp: null
  name: bkgatewayinstances.gateway.bk.tencent.com
spec:
  group: gateway.bk.tencent.com
  names:
    kind: BkGatewayInstance
    listKind: BkGatewayInstanceList
    plural: bkgatewayinstances
    singular: bkgatewayinstance
  scope: Namespaced
  validation:
    openAPIV3Schema:
      description: BkGatewayInstance is the Schema for the bkgatewayinstances API
      properties:
        apiVersion:
          description: 'APIVersion defines the versioned schema of this representation
            of an object. Servers should convert recognized schemas to the latest
            internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
          type: string
        kind:
          description: 'Kind is a string value representing the REST resource this
            object represents. Servers may infer this from the endpoint the client
            submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
          type: string
        metadata:
          type: object
        spec:
          description: BkGatewayInstanceSpec defines the desired state of BkGatewayInstance
          properties:
            reportInterval:
              type: string
          type: object
        status:
          description: BkGatewayInstanceStatus defines the observed state of BkGatewayInstance
          properties:
            controlPlane:
              properties:
                curConfigVersion:
                  type: string
                discoveryPlugins:
                  items:
                    properties:
                      discoveryType:
                        type: string
                      message:
                        type: string
                      name:
                        type: string
                      readyUntil:
                        format: date-time
                        type: string
                      services:
                        items:
                          type: string
                        type: array
                      status:
                        type: string
                    type: object
                  type: array
                effectiveConfigVersion:
                  type: string
                message:
                  type: string
                status:
                  type: integer
              type: object
            dataPlane:
              description: 'INSERT ADDITIONAL STATUS FIELD - define observed state
                of cluster Important: Run "make" to regenerate code after modifying
                this file'
              properties:
                apisixVersion:
                  type: string
                configCenter:
                  type: string
                message:
                  type: string
                pluginSchema:
                  type: object
                status:
                  type: integer
                type:
                  type: string
              type: object
            lastUpdateTime:
              format: date-time
              type: string
          type: object
      type: object
  version: v1beta1
  versions:
  - name: v1beta1
    served: true
    storage: true
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
{{- end }}
