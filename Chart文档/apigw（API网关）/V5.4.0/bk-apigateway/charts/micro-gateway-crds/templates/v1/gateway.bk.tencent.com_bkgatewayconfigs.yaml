{{- if .Capabilities.APIVersions.Has "apiextensions.k8s.io/v1" -}}
---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.9.2
    helm.sh/resource-policy: keep
  creationTimestamp: null
  name: bkgatewayconfigs.gateway.bk.tencent.com
spec:
  group: gateway.bk.tencent.com
  names:
    kind: BkGatewayConfig
    listKind: BkGatewayConfigList
    plural: bkgatewayconfigs
    singular: bkgatewayconfig
  scope: Namespaced
  versions:
  - name: v1beta1
    schema:
      openAPIV3Schema:
        description: BkGatewayConfig is the Schema for the bkgatewayconfigs API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: BkGatewayConfigSpec defines the desired state of BkGatewayConfig
            properties:
              controller:
                description: BkGatewayConfigController controller field for bk gateway
                  config
                properties:
                  basePath:
                    description: EdgeController server base path
                    type: string
                  endpoints:
                    description: EdgeController server endpints
                    items:
                      type: string
                    type: array
                  jwtAuth:
                    description: JwtAuth jwt auth config
                    properties:
                      key:
                        description: Key key for jwt auth
                        type: string
                      secret:
                        description: Secret secret for jwt auth
                        type: string
                    type: object
                type: object
              desc:
                type: string
              instanceID:
                nullable: true
                type: string
              name:
                description: Controller controller config
                type: string
            type: object
          status:
            description: BkGatewayConfigStatus defines the observed state of BkGatewayConfig
            properties:
              message:
                description: Message message for bk gateway
                type: string
              status:
                description: Status status for bk gateway
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
{{- end }}
