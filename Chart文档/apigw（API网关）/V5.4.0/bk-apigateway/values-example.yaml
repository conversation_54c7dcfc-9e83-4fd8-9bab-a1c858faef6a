## 此文件描述最小配置项，可用于测试
## 加密密钥，敏感；配置后不可修改，否则会导致加密数据异常
##
encryptKey: "M2ZHM0JMMTJWZml2ZFVZWTE2WVJTUVh0ck1aM0U2ZTQ="
keys:
  ## 应用 bk_apigateway 的密钥
  ##
  apigatewayAppSecret: "app secret for bk_apigateway"
  ## 应用 bk_apigw_test 的密钥
  ##
  apigatewayTestAppSecret: "app secret for bk_apigw_test"
  ## 应用 bk_paas 的密钥
  ##
  paas2AppSecret: "app secret for bk_paas"

## 默认进程副本数
##
replicaCount: 1

## 是否创建网关 CRD
##
registerCrds: false

## 服务账户
##
serviceAccount:
  ## 是否创建
  ##
  create: false

apigateway:
  ## 默认集群内 Service 类型
  ##
  bkapiServiceType: NodePort

## 内建数据库
##
mariadb:
  primary:
    persistence:
      enabled: false

## 内建 ETCD
##
etcd:
  persistence:
    enabled: false
