{{- $apigatewayAppCode := .Values.keys.apigatewayAppCode | required "Values.keys.apigatewayAppCode is required" -}}
{{- $apigatewayAppSecret := .Values.keys.apigatewayAppSecret | required "Values.keys.apigatewayAppSecret is required" -}}
{{- $apigatewayTestAppCode := .Values.keys.apigatewayTestAppCode | required "Values.keys.apigatewayTestAppCode is required" -}}
{{- $apigatewayTestAppSecret := .Values.keys.apigatewayTestAppSecret | required "Values.keys.apigatewayTestAppSecret is required" -}}
{{- $esbToken := .Values.keys.paas2AppSecret | required "Values.keys.paas2AppSecret is required" -}}
{{- $bkdomain := .Values.global.bkDomain | required "Values.keys.bkDomain is required" -}}

{{/* 内部模块地址 */}}

{{- $bkapiUrl := include "apigw-helm-stacks.ingress-url" (list .Values.apigateway.ingress .Values.global) }}
{{- $dashboardFeUrl := include "apigw-helm-stacks.dashboard-fe.ingress-url" . }}
{{- $dashboardUrl := include "apigw-helm-stacks.ingress-url" (list .Values.dashboard.ingress .Values.global) }}
{{- $dashboardRootUrl := include "apigw-helm-stacks.dashboard-ingress-url-root-path" (list .Values.dashboard.ingress .Values.global) }}


{{/* 通用外部依赖 */}}
{{- $bkLoginApiUrl := .Values.bkLoginApiUrl | required "Values.bkLoginApiUrl is required" -}}
{{- $bkLoginUrl := .Values.bkLoginUrl | default (include "apigw-helm-stacks.urljoin" (list $bkLoginApiUrl "login")) -}}
{{- $bkAuthApiUrl := .Values.bkAuthApiUrl | required "Values.bkAuthApiUrl is required" -}}
{{- $bkSsmApiUrl := .Values.bkSsmApiUrl | required "Values.bkSsmApiUrl is required" -}}
{{- $bkDocsCenterUrl := .Values.bkDocsCenterUrl -}}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-basic-env
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
data:
  PRE_ENCRYPT_KEY: {{ .Values.encrypt.crypto.key }}
  PRE_ENCRYPT_MARK: {{ .Values.encrypt.crypto.mark }}
  {{/* 服务依赖 */}}
  {{/* apigw 数据库 */}}
  {{- $apigwDB := fromYaml (include "apigw-helm-stacks.databaseApigw" .) -}}
  BK_APIGW_DATABASE_NAME: "{{ $apigwDB.name }}"
  BK_APIGW_DATABASE_USER: "{{ $apigwDB.user }}"
  BK_APIGW_DATABASE_PASSWORD: "{{ $apigwDB.password }}"
  BK_APIGW_DATABASE_HOST: "{{ $apigwDB.host }}"
  BK_APIGW_DATABASE_PORT: "{{ $apigwDB.port }}"

  {{/* esb 数据库 */}}
  {{- $esbDB := fromYaml (include "apigw-helm-stacks.databaseEsb" .) -}}
  BK_ESB_DATABASE_NAME: "{{ $esbDB.name }}"
  BK_ESB_DATABASE_USER: "{{ $esbDB.user }}"
  BK_ESB_DATABASE_PASSWORD: "{{ $esbDB.password }}"
  BK_ESB_DATABASE_HOST: "{{ $esbDB.host }}"
  BK_ESB_DATABASE_PORT: "{{ $esbDB.port }}"

  {{/* legacyEsb 数据库，不配置名称时不渲染环境变量 */}}
  {{- $legacyEsbDB := fromYaml (include "apigw-helm-stacks.database.legacy-esb" .) -}}
  {{- if $legacyEsbDB.name }}
  BK_PAAS2_DATABASE_NAME: "{{ $legacyEsbDB.name }}"
  BK_PAAS2_DATABASE_USER: "{{ $legacyEsbDB.user }}"
  BK_PAAS2_DATABASE_PASSWORD: "{{ $legacyEsbDB.password }}"
  BK_PAAS2_DATABASE_HOST: "{{ $legacyEsbDB.host }}"
  BK_PAAS2_DATABASE_PORT: "{{ $legacyEsbDB.port }}"
  {{- end }}

  {{/* apigw redis */}}
  {{- $apigwRedis := fromYaml (include "apigw-helm-stacks.redis.apigw" .) -}}
  BK_APIGW_REDIS_HOST: "{{ $apigwRedis.host }}"
  BK_APIGW_REDIS_PORT: "{{ $apigwRedis.port }}"
  BK_APIGW_REDIS_PASSWORD: "{{ $apigwRedis.password }}"
  BK_APIGW_REDIS_DB: "{{ $apigwRedis.db }}"
  {{- if eq $apigwRedis.type "sentinel" }}
  BK_APIGW_REDIS_USE_SENTINEL: "true"
  {{- else }}
  BK_APIGW_REDIS_USE_SENTINEL: "false"
  {{- end }}
  BK_APIGW_REDIS_SENTINEL_PASSWORD: "{{ $apigwRedis.sentinelPassword }}"
  BK_APIGW_REDIS_SENTINEL_MASTER_NAME: "{{ $apigwRedis.masterName }}"
  BK_APIGW_REDIS_SENTINEL_ADDR: "{{ $apigwRedis.sentinelAddr }}"

  {{/* apigw rabbitmq */}}
  {{- $apigwRabbitmq := fromYaml (include "apigw-helm-stacks.rabbitmq.apigw" .) -}}
  BK_APIGW_RABBITMQ_HOST: "{{ $apigwRabbitmq.host }}"
  BK_APIGW_RABBITMQ_PORT: "{{ $apigwRabbitmq.port }}"
  BK_APIGW_RABBITMQ_USER: "{{ $apigwRabbitmq.username }}"
  BK_APIGW_RABBITMQ_PASSWORD: "{{ $apigwRabbitmq.password }}"
  BK_APIGW_RABBITMQ_VHOST: "{{ $apigwRabbitmq.vhost}}"

  {{/* apigw elasticsearch */}}
  {{- $apigwElasticsearch := fromYaml (include "apigw-helm-stacks.elasticsearch.apigw" .) -}}
  BK_APIGW_ES_HOST: "{{ $apigwElasticsearch.host }}"
  BK_APIGW_ES_PORT: "{{ $apigwElasticsearch.port }}"
  BK_APIGW_ES_USER: "{{ $apigwElasticsearch.user }}"
  BK_APIGW_ES_PASSWORD: "{{ $apigwElasticsearch.password }}"

  {{/* 默认配置 */}}
  BKPAAS_ENVIRONMENT: "prod"
  DEBUG: "{{ .Values.debug | default false }}"
  BK_APIGW_SENTRY_DSN: "{{ .Values.sentryDsn }}"
  BK_APP_CODE: {{ $apigatewayAppCode }}
  BK_APP_SECRET: {{ $apigatewayAppSecret }}
  ESB_TOKEN: {{ $esbToken }}
  DEFAULT_TEST_APP_CODE: {{ $apigatewayTestAppCode }}
  DEFAULT_TEST_APP_SECRET: {{ $apigatewayTestAppSecret }}
  SECRET_KEY: {{ $apigatewayAppSecret }}
  APIGW_MANAGERS: {{ join "," .Values.managers }}
  DASHBOARD_CSRF_COOKIE_DOMAIN: ".{{- $bkdomain -}}"
  DASHBOARD_SESSION_COOKIE_NAME: "bk_apigw_dashboard_sessionid"
  DASHBOARD_CSRF_COOKIE_NAME: "bk_apigw_dashboard_csrftoken"
  BK_ESB_ES_CLIENT_TYPE: "bk_log"
  BK_ESB_ES_TIME_FIELD_NAME: "dtEventTimeStamp"
  BK_ESB_API_LOG_ES_INDEX: "{{ .Values.dashboard.bkEsbApiLogEsIndex }}"
  BK_APIGW_ES_TIME_FIELD_NAME: "dtEventTimeStamp"
  BK_APIGW_ES_CLIENT_TYPE: "bk_log"
  BK_APIGW_API_LOG_ES_INDEX: "{{ .Values.dashboard.bkApigwApiLogEsIndex }}"
  BK_APIGW_JWT_ISSUER: "{{ .Values.dashboard.jwtIssuer }}"
  BK_APIGATEWAY_VERSION: "{{ .Chart.AppVersion }}"
  {{- if .Values.bkEsb.bkLogConfig.enabled }}
  BK_ESB_LOG_PATH: "{{ .Values.bkEsb.bkLogConfig.logPath }}"
  {{- end }}

  {{/* 模块地址 */}}
  DASHBOARD_FE_URL: {{ $dashboardFeUrl }}
  DASHBOARD_URL: {{ $dashboardUrl }}
  DASHBOARD_INNER_URL: http://{{ include "apigw-helm-stacks.name-prefix" . }}-dashboard:{{ .Values.dashboard.service.httpPort }}

  {{/* 访问模板 */}}
  BK_API_URL_TMPL: {{ $bkapiUrl }}/api/{api_name}/
  BK_API_INNER_URL_TMPL: http://{{ .Values.apigateway.bkapiServiceName }}/api/{api_name}/
  API_RESOURCE_URL_TMPL: {{ $bkapiUrl }}/api/{api_name}/{stage_name}/{resource_path}
  API_DOCS_URL_TMPL: {{ $dashboardFeUrl }}/docs/apigw-api/{api_name}/intro
  RESOURCE_DOC_URL_TMPL: {{ $dashboardFeUrl }}/docs/apigw-api/{api_name}/{resource_name}/doc?stage={stage_name}
  COMPONENT_DOC_URL_TMPL: {{ $dashboardFeUrl }}/docs/component-api/{board}/{system_name}/{component_name}/doc

  {{/* 外部依赖 */}}
  BK_LOGIN_SIGN_IN_URL: "{{ $bkLoginUrl }}"
  BK_AUTH_API_URL: "{{ $bkAuthApiUrl }}"
  BK_SSM_API_URL: "{{ $bkSsmApiUrl }}"
  BK_DOCS_URL_PREFIX: "{{ $bkDocsCenterUrl }}"
  BK_COMPONENT_API_URL: "{{ .Values.bkComponentApiUrl | default (printf "%s://%s" .Values.global.bkDomainScheme (include "apigw-helm-stacks.ingress-host" (list .Values.apigateway.ingress .Values.global))) }}"
  BK_COMPONENT_API_INNER_URL: "{{ printf "http://%s-bk-esb:%v" (include "apigw-helm-stacks.name-prefix" .) .Values.bkEsb.service.httpPort }}"
  BK_PAAS3_URL: "{{ .Values.bkPaas3Url | default (printf "%s://bkpaas.%s" .Values.global.bkDomainScheme .Values.global.bkDomain) }}"

  {{/* ESB 外部依赖 */}}
  BK_PAAS_URL: "{{ .Values.bkPaas2Url | default $bkLoginApiUrl }}"
  BK_PAAS_LOGIN_URL: "{{ $bkLoginApiUrl }}"
  BK_IAM_URL: "{{ .Values.bkIamSaasApiUrl }}"
  BK_DOCS_CENTER_URL: "{{ .Values.bkDocsCenterApiUrl }}"
  BK_GSEKIT_URL: "{{ .Values.bkGsekitUrl }}"
  BK_ITSM_URL: "{{ .Values.bkItsmUrl }}"
  BK_SOPS_URL: "{{ .Values.bkSopsUrl }}"
  BK_LOG_SEARCH_URL: "{{ .Values.bkLogSearchApiUrl }}"
  BK_CMDB_V3_URL: "{{ .Values.bkCmdbApiUrl }}"
  BK_JOB_URL: "{{ .Values.bkJobApiBackendUrl }}"
  BK_GSE_PROC_HOST: "{{ .Values.bkGseProcHost }}"
  BK_GSE_PROC_PORT: "{{ .Values.bkGseProcPort }}"
  BK_GSE_CACHEAPI_HOST: "{{ .Values.bkGseCacheapiHost }}"
  BK_GSE_CACHEAPI_PORT: "{{ .Values.bkGseCacheapiPort }}"
  BK_GSE_PMS_URL: "{{ .Values.bkGsePmsUrl }}"
  BK_GSE_CONFIG_URL: "{{ .Values.bkGseConfigUrl }}"
  BK_DATA_URL: "{{ .Values.bkDataUrl }}"
  BK_DATA_BKSQL_URL: "{{ .Values.bkDataBksqlUrl }}"
  BK_DATA_PROCESSORAPI_URL: "{{ .Values.bkDataProcessorapiUrl }}"
  BK_DATA_MODELFLOW_URL: "{{ .Values.bkDataModelflowUrl }}"
  BK_DATA_V3_AUTHAPI_URL: "{{ .Values.bkDataV3AuthapiUrl }}"
  BK_DATA_V3_ACCESSAPI_URL: "{{ .Values.bkDataV3AccessapiUrl }}"
  BK_DATA_V3_DATABUSAPI_URL: "{{ .Values.bkDataV3DatabusapiUrl }}"
  BK_DATA_V3_DATAFLOWAPI_URL: "{{ .Values.bkDataV3DataflowapiUrl }}"
  BK_DATA_V3_DATAMANAGEAPI_URL: "{{ .Values.bkDataV3DatamanageapiUrl }}"
  BK_DATA_V3_DATAQUERYAPI_URL: "{{ .Values.bkDataV3DataqueryapiUrl }}"
  BK_DATA_V3_METAAPI_URL: "{{ .Values.bkDataV3MetaapiUrl }}"
  BK_DATA_V3_STOREKITAPI_URL: "{{ .Values.bkDataV3StorekitapiUrl }}"
  BK_DATA_V3_BKSQL_URL: "{{ .Values.bkDataV3BksqlUrl }}"
  BK_DATA_V3_MODELAPI_URL: "{{ .Values.bkDataV3ModelapiUrl }}"
  BK_DATA_V3_DATACUBEAPI_URL: "{{ .Values.bkDataV3DatacubeapiUrl }}"
  BK_DATA_V3_ALGORITHMAPI_URL: "{{ .Values.bkDataV3AlgorithmapiUrl }}"
  BK_DATA_V3_DATALABAPI_URL: "{{ .Values.bkDataV3DatalabapiUrl }}"
  BK_DATA_V3_AIOPSAPI_URL: "{{ .Values.bkDataV3AiopsapiUrl }}"
  BK_DATA_V3_RESOURCECENTERAPI_URL: "{{ .Values.bkDataV3ResourcecenterapiUrl }}"
  BK_DATA_V3_QUERYENGINEAPI_URL: "{{ .Values.bkDataV3QueryengineapiUrl }}"
  BK_DATA_V3_LANGSERVER_URL: "{{ .Values.bkDataV3LangserverUrl }}"

  BK_FTA_URL: "{{ .Values.bkFtaUrl }}"
  BK_DEVOPS_URL: "{{ .Values.bkDevopsUrl }}"
  BK_CICDKIT_URL: "{{ .Values.bkCicdkitUrl }}"
  BK_MONITOR_URL: "{{ .Values.bkMonitorUrl }}"
  BK_MONITOR_V3_URL: "{{ .Values.bkMonitorApiUrl }}"
  BK_USERMGR_URL: "{{ .Values.bkUserApiUrl }}"
  BK_LOG_URL: "{{ .Values.bkLogSearchApiUrl }}"
  BK_NODEMAN_URL: "{{ .Values.bkNodemanApiUrl }}"
  BK_BSCP_API_URL: "{{ .Values.bkBscpApiUrl }}"

  {{/* bkrepo 相关配置 */}}
  {{ with .Values.bkrepoConfig }}
  {{ $pypiRepositoryUrl := include "apigw-helm-stacks.urljoin" (list .endpoint "pypi" .bkpaas3Project .pypiRepository) }}
  FEATURE_FLAG_ENABLE_SDK: "{{ .enabled }}"
  FEATURE_FLAG_ALLOW_UPLOAD_SDK_TO_REPOSITORY: "{{ .enabled }}"
  DEFAULT_PYPI_REPOSITORY_URL: "{{ $pypiRepositoryUrl }}"
  DEFAULT_PYPI_INDEX_URL: "{{ include "apigw-helm-stacks.urljoin" (list $pypiRepositoryUrl "simple") }}"
  DEFAULT_PYPI_USERNAME: "{{ .bkpaas3Username }}"
  DEFAULT_PYPI_PASSWORD: "{{ .bkpaas3Password }}"
  {{ end }}

  {{/* 蓝鲸调用链 */}}
  {{- $dashboardTrace := .Values.dashboard.trace -}}
  BK_APIGW_ENABLE_OTEL_TRACE: "{{ .Values.global.trace.enabled }}"
  BK_APIGW_OTEL_TYPE: "{{ .Values.global.trace.otlp.type }}"
  BK_APIGW_OTEL_HTTP_URL: "{{ include "apigw-helm-stacks.trace.http-url" . }}"
  BK_APIGW_OTEL_DATA_TOKEN: "{{ .Values.global.trace.otlp.token }}"
  DASHBOARD_OTEL_SAMPLER: "{{ $dashboardTrace.sampler }}"
  DASHBOARD_OTEL_SERVICE_NAME: "{{ $dashboardTrace.serviceName }}"
  DASHBOARD_OTEL_INSTRUMENT_DB_API: "{{ $dashboardTrace.instrument.dbApi }}"
  DASHBOARD_OTEL_INSTRUMENT_CELERY: "{{ $dashboardTrace.instrument.celery }}"
  DASHBOARD_OTEL_INSTRUMENT_REDIS: "{{ $dashboardTrace.instrument.redis }}"

  {{/* 微网关相关配置 */}}
  DEFAULT_MICRO_GATEWAY_ID: "{{ include "apigw-helm-stacks.micro-gateway.instance-id" . }}"
  DEFAULT_MICRO_GATEWAY_NAME: "{{ include "apigw-helm-stacks.micro-gateway.instance-name" . }}"
  DEFAULT_MICRO_GATEWAY_API_NAME: "{{ include "apigw-helm-stacks.micro-gateway.api-name" . }}"
  DEFAULT_MICRO_GATEWAY_STAGE_NAME: "{{ include "apigw-helm-stacks.micro-gateway.stage-name" . }}"
  DEFAULT_MICRO_GATEWAY_SECRET: "{{ $apigatewayAppSecret }}"
  DEFAULT_MICRO_GATEWAY_HTTP_URL: "{{ $bkapiUrl }}"

  RUN_IN_CONTAINER: "1"
