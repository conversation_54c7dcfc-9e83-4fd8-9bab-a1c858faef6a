apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "apigw-helm-stacks.job-name" (list . "wait-migrations") }}
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  backoffLimit: 10
  parallelism: 1
  template:
    metadata:
      annotations: {{ include "apigw-helm-stacks.pod-annotations" . | nindent 8 }}
      labels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 8 }}
        app.kubernetes.io/component: "basic"
    spec:
      serviceAccountName: {{ include "apigw-helm-stacks.service-account-name" . }}
      affinity: {{ include "apigw-helm-stacks.affinity" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      tolerations: {{ include "apigw-helm-stacks.tolerations" . | nindent 8 }}
      initContainers:
        {{- include "apigw-helm-stacks.wait-for-static-job-init-container" (list . "wait-storages") | nindent 8 }}
        {{- if .Values.dashboard.enabled }}
        {{- include "apigw-helm-stacks.wait-for-job-init-container" (list . "dashboard-migrate") | nindent 8 }}
        {{- end }}
      containers:
        - name: log
          image: {{ include "apigw-helm-stacks.image" (list  .Values.k8sWaitFor.image .Values.global) }}
          imagePullPolicy: {{ .Values.k8sWaitFor.image.pullPolicy }}
          resources: {{ toYaml .Values.k8sWaitFor.resources | nindent 12 }}
          command: ["date"]
      imagePullSecrets: {{ include "apigw-helm-stacks.image-pull-secrets" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      restartPolicy: Never
      securityContext: {{ include "apigw-helm-stacks.container-security-context" . | nindent 8 }}
