apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "apigw-helm-stacks.static-job-name" (list . "wait-storages") }}
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  backoffLimit: 10
  parallelism: 1
  template:
    metadata:
      annotations: {{ include "apigw-helm-stacks.pod-annotations" . | nindent 8 }}
      labels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 8 }}
        app.kubernetes.io/component: "basic"
    spec:
      serviceAccountName: {{ include "apigw-helm-stacks.service-account-name" . }}
      affinity: {{ include "apigw-helm-stacks.affinity" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      tolerations: {{ include "apigw-helm-stacks.tolerations" . | nindent 8 }}
      initContainers:
        {{- if .Values.initContainers }}
        {{- include "common.tplvalues.render" (dict "value" .Values.initContainers "context" $) | nindent 8 }}
        {{- end }}
        {{- if .Values.mariadb.enabled }}
        {{- include "apigw-helm-stacks.wait-for-pod-init-container" (list . (printf "%s-0" (include "apigw-helm-stacks.mariadbName" .))) | nindent 8 }}
        {{- end }}
        {{- if .Values.redis.enabled }}
        {{- include "apigw-helm-stacks.wait-for-pod-init-container" (list . (printf "%s-0" (include "apigw-helm-stacks.redisName" .))) | nindent 8 }}
        {{- end }}
        {{- if .Values.etcd.enabled }}
        {{- include "apigw-helm-stacks.wait-for-pod-init-container" (list . (printf "%s-0" (include "apigw-helm-stacks.etcdName" .))) | nindent 8 }}
        {{- end }}
      containers:
        - name: log
          image: {{ include "apigw-helm-stacks.image" (list  .Values.k8sWaitFor.image .Values.global) }}
          imagePullPolicy: {{ .Values.k8sWaitFor.image.pullPolicy }}
          resources: {{ toYaml .Values.k8sWaitFor.resources | nindent 12 }}
          command: ["date"]
      imagePullSecrets: {{ include "apigw-helm-stacks.image-pull-secrets" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      restartPolicy: Never
      securityContext: {{ include "apigw-helm-stacks.container-security-context" . | nindent 8 }}
