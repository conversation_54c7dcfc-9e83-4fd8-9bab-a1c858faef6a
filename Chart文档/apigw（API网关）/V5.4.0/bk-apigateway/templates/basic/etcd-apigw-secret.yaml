{{- if .Values.operator.enabled }}
{{- $apigwEtcd := fromYaml (include "apigw-helm-stacks.etcd.apigw" .) -}}
{{- if $apigwEtcd.certBase64Encoded }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ $apigwEtcd.tlsCertSecret }}
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
data:
  {{- if $apigwEtcd.caBase64Encoded }}
  {{ $apigwEtcd.caCertFileName }}: {{ $apigwEtcd.caBase64Encoded }}
  {{- end }}
  {{- if $apigwEtcd.certBase64Encoded }}
  {{ $apigwEtcd.certFileName }}: {{ $apigwEtcd.certBase64Encoded }}
  {{- end }}
  {{- if $apigwEtcd.keyBase64Encoded }}
  {{ $apigwEtcd.keyFileName }}: {{ $apigwEtcd.keyBase64Encoded }}
  {{- end }}
{{- end }}
{{- end }}
