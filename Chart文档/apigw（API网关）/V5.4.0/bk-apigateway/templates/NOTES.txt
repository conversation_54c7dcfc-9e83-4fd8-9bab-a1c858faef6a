{{- $dashboardUrl := include "apigw-helm-stacks.ingress-url" (list .Values.dashboard.ingress .Values.global) -}}
{{- $dashboardFeUrl := include "apigw-helm-stacks.dashboard-fe.ingress-url" . -}}
{{- $apigatewayUrl := include "apigw-helm-stacks.ingress-url" (list .Values.apigateway.ingress .Values.global) -}}


感谢安装 {{ .Chart.Name }}，对应的 Release 为 {{ .Release.Name }}。

访问以下链接进入 API 网关主站： {{ $dashboardFeUrl }}。
下方列表展示各个模块及其访问地址，请确保对应的域名都解析到 INGRESS IP：
- 云 API 访问地址：{{ $apigatewayUrl }}
- API 网关主站：{{ $dashboardFeUrl }}
- API 网关主站后端接口：{{ $dashboardUrl }}

如需确认服务是否正常，请等待所有 Pod 就绪后执行以下命令：
  helm test {{ .Release.Name }} -n {{ .Release.Namespace }}

输出的所有 Phase 都为 Succeeded 即说明正常。

如需卸载，请执行：
  helm uninstall {{ .Release.Name }} -n {{ .Release.Namespace }}

如需导入自定义组件，请使用 chart 内提供的 esb-custom-component-configmap-updater.sh 脚本，执行：
  esb-custom-component-configmap-updater.sh -n {{ .Release.Namespace }} -r path/to/components/generic/apis
把命令输出的 bkEsb.customComponentConfigMaps 配置更新到 values 中，重新发布后，ESB 会将代码挂载到 components/generic/apis/ 目录下。
