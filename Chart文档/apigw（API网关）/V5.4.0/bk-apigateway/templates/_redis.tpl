{{/*
内建 Redis 名称
*/}}
{{- define "apigw-helm-stacks.redisName" -}}
{{- printf "%s-master" (include "common.names.fullname" (dict "Values" .Values.redis "Chart" .Chart "Release" .Release)) -}}
{{- end -}}

{{/*
默认 Redis 基础配置，处理了内建和外部 Redis 场景
*/}}
{{- define "apigw-helm-stacks.redis" -}}
{{- $root := first . -}}
{{- $name := last . -}}
{{- $values := $root.Values -}}
{{- $redis := index $values.externalRedis $name -}}

{{- if $values.redis.enabled -}}
db: 0
password: {{ $values.redis.auth.password | required "redis.auth.password is required" }}
host: {{ include "apigw-helm-stacks.redisName" $root }}
port: {{ $values.redis.master.service.port | required "redis.master.service.port is required" }}
type: "standalone"
sentinelAddr: ""
masterName: ""
sentinelPassword: ""

{{- else -}}
{{- $redisDefault := $values.externalRedis.default -}}

{{- if hasKey $redis "db" }}
db: {{ $redis.db }}
{{- else }}
db: {{ $redisDefault.db }}
{{- end }}
password: {{ $redis.password | default $redisDefault.password | required (printf "externalRedis.%s.password is required" $name) }}
host: {{ $redis.host | default $redisDefault.host | required (printf "externalRedis.%s.host is required" $name) }}
port: {{ $redis.port | default $redisDefault.port | required (printf "externalRedis.%s.port is required" $name) }}
{{/*
{{- if $redisDefault.sentinel.enabled }}
type: "sentinel"
sentinelAddr: "{{ $redisDefault.sentinel.hosts }}"
masterName: "{{ $redisDefault.sentinel.masterSet }}"
sentinelPassword: "{{ $redisDefault.sentinel.password }}"
{{- else }}
*/}}
type: "standalone"
sentinelAddr: ""
masterName: ""
sentinelPassword: ""
{{/*
{{- end }}
*/}}

{{- end -}}
{{- end -}}

{{/*
apigw Redis 配置，处理默认 Redis 合并逻辑
*/}}
{{- define "apigw-helm-stacks.redis.apigw" -}}
{{ include "apigw-helm-stacks.redis" (list . "apigw") }}
{{- end -}}

{{/*
apisix 插件 Redis 配置，处理默认 Redis 合并逻辑
*/}}
{{- define "apigw-helm-stacks.redis.apisix" -}}
{{ include "apigw-helm-stacks.redis" (list . "apisix") }}
{{- end -}}