{{- $apigatewayTestAppCode := .Values.keys.apigatewayTestAppCode | required "Values.keys.apigatewayTestAppCode is required" -}}

{{/* 内部模块地址 */}}
{{- $bkapiUrl := include "apigw-helm-stacks.ingress-url" (list .Values.apigateway.ingress .Values.global) }}
{{- $dashboardFeUrl := include "apigw-helm-stacks.dashboard-fe.ingress-url" . }}
{{- $dashboardUrl := include "apigw-helm-stacks.ingress-url" (list .Values.dashboard.ingress .Values.global) }}
{{- $dashboardRootUrl := include "apigw-helm-stacks.dashboard-ingress-url-root-path" (list .Values.dashboard.ingress .Values.global) }}

{{/* 通用外部依赖 */}}
{{- $bkLoginApiUrl := .Values.bkLoginApiUrl | required "Values.bkLoginApiUrl is required" -}}
{{- $bkLoginUrl := .Values.bkLoginUrl | default (include "apigw-helm-stacks.urljoin" (list $bkLoginApiUrl "login")) -}}
{{- $bkDocsCenterUrl := .Values.bkDocsCenterUrl -}}
{{- $bkdomain := .Values.global.bkDomain | required "Values.keys.bkDomain is required" -}}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-dashboard-fe-env
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
data:
  DEFAULT_TEST_APP_CODE: {{ $apigatewayTestAppCode }}
  DASHBOARD_CSRF_COOKIE_NAME: "bk_apigw_dashboard_csrftoken"
  BK_APIGATEWAY_VERSION: "{{ .Chart.AppVersion }}"
  DASHBOARD_FE_URL: {{ $dashboardFeUrl }}
  DASHBOARD_URL: {{ $dashboardUrl }}
  API_RESOURCE_URL_TMPL: {{ $bkapiUrl }}/api/{api_name}/{stage_name}/{resource_path}
  BK_LOGIN_SIGN_IN_URL: "{{ $bkLoginUrl }}"
  BK_DOCS_URL_PREFIX: "{{ $bkDocsCenterUrl }}"
  BK_COMPONENT_API_URL: "{{ .Values.bkComponentApiUrl | default (printf "%s://%s" .Values.global.bkDomainScheme (include "apigw-helm-stacks.ingress-host" (list .Values.apigateway.ingress .Values.global))) }}"
  BK_DOMAIN: "{{ $bkdomain }}"

