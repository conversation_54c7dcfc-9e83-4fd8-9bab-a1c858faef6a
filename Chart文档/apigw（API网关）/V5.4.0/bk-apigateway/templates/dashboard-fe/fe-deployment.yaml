{{- if .Values.dashboardFe.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-dashboard-fe
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  replicas: {{ include "apigw-helm-stacks.deployment-replicas" (list .Values.dashboardFe .Values) }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 0
  selector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "dashboard-fe"
  template:
    metadata:
      annotations:
        checksum/configmap: {{ include (print $.Template.BasePath "/dashboard-fe/fe-configmap.yaml") . | sha256sum }}
        {{- if .Values.podAnnotations  }}
        {{- include "apigw-helm-stacks.pod-annotations" . | nindent 8 }}
        {{- end }}
      labels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 8 }}
        app.kubernetes.io/component: "dashboard-fe"
    spec:
      affinity: {{ include "apigw-helm-stacks.affinity" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      tolerations: {{ include "apigw-helm-stacks.tolerations" . | nindent 8 }}
      hostAliases: {{ include "apigw-helm-stacks.host-aliases" . | nindent 8 }}
      containers:
      - args:
        - run
        - server
        command:
        - npm
        env: {{ include "apigw-helm-stacks.yaml" .Values.dashboardFe.extraEnvVars | nindent 10 }}
          - name: PORT
            value: "{{ .Values.dashboardFe.service.httpPort }}"
        envFrom:
        - configMapRef:
            name: {{ include "apigw-helm-stacks.name-prefix" . }}-dashboard-fe-env
        image: {{ include "apigw-helm-stacks.image" (list  .Values.dashboardFe.image .Values.global) }}
        imagePullPolicy: {{ include "apigw-helm-stacks.image-pull-policy" .Values.dashboardFe.image }}
        livenessProbe:
          failureThreshold: 3
          periodSeconds: 30
          tcpSocket:
            port: http
        name: bk-apigateway-dashboard-fe
        ports:
        - containerPort: {{ .Values.dashboardFe.service.httpPort }}
          name: http
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          periodSeconds: 10
          tcpSocket:
            port: http
        resources: {{ include "apigw-helm-stacks.container-resources" .Values.dashboardFe | nindent 10 }}
        volumeMounts: {{ include "apigw-helm-stacks.yaml" .Values.dashboardFe.extraVolumeMounts | nindent 10 }}
      volumes: {{ include "apigw-helm-stacks.yaml" .Values.dashboardFe.extraVolumes | nindent 8 }}
      restartPolicy: Always
      imagePullSecrets: {{ include "apigw-helm-stacks.image-pull-secrets" . | nindent 8 }}
      securityContext: {{ include "apigw-helm-stacks.container-security-context" . | nindent 8 }}
{{- end }}
