{{- if .Values.dashboardFe.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-dashboard-fe
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  type: {{ .Values.dashboardFe.service.type }}
  selector: {{ include "apigw-helm-stacks.service-selector-labels" . | nindent 4 }}
    app.kubernetes.io/component: "dashboard-fe"
  ports:
    - name: http
      port: {{ .Values.dashboardFe.service.httpPort }}
      protocol: TCP
      targetPort: http
{{- end }}