{{- if and .Values.operator.enabled .Values.operator.bkLogConfig.enabled }}
# stdout Log
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-operator-stdout
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  dataId: {{ .Values.operator.bkLogConfig.stdoutDataId }}
  encoding: {{ .Values.operator.bkLogConfig.stdoutEncoding }}
  logConfigType: std_log_config
  namespace: {{ .Release.Namespace }}
  labelSelector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "operator"
---
# Container Log
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-operator-file
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  dataId: {{ .Values.operator.bkLogConfig.fileDataId | default .Values.operator.bkLogConfig.stdoutDataId }}
  encoding: {{ .Values.operator.bkLogConfig.fileEncoding | default .Values.operator.bkLogConfig.stdoutEncoding }}
  logConfigType: container_log_config
  namespace: {{ .Release.Namespace }}
  labelSelector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "operator"
  path:
    - /app/logs/*.log
{{- end }}