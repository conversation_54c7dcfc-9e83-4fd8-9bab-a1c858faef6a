{{- if .Values.operator.enabled }}
{{- $apigwEtcd := fromYaml (include "apigw-helm-stacks.etcd.apigw" .) -}}
{{- $apisixEtcd := fromYaml (include "apigw-helm-stacks.etcd.apisix" .) -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-operator
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  replicas: {{ include "apigw-helm-stacks.deployment-replicas" (list .Values.operator .Values) }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 0
  selector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "operator"
  template:
    metadata:
      annotations:
        checksum/configmap: {{ include (print $.Template.BasePath "/operator/operator-configmap.yaml") . | sha256sum }}
        {{- if $apisixEtcd.tlsCertSecret }}
        checksum/etcd-apisix-secret: {{ include (print $.Template.BasePath "/basic/etcd-apisix-secret.yaml") . | sha256sum }}
        {{- end }}
        {{- if $apigwEtcd.tlsCertSecret }}
        checksum/etcd-apigw-secret: {{ include (print $.Template.BasePath "/basic/etcd-apigw-secret.yaml") . | sha256sum }}
        {{- end }}
        {{- if .Values.podAnnotations  }}
        {{- include "apigw-helm-stacks.pod-annotations" . | nindent 8 }}
        {{- end }}
      labels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 8 }}
        app.kubernetes.io/component: "operator"
    spec:
      serviceAccountName: {{ include "apigw-helm-stacks.service-account-name" . }}
      affinity: {{ include "apigw-helm-stacks.affinity" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      tolerations: {{ include "apigw-helm-stacks.tolerations" . | nindent 8 }}
      hostAliases: {{ include "apigw-helm-stacks.host-aliases" . | nindent 8 }}
      initContainers: {{ include "apigw-helm-stacks.wait-for-storages-job-init-container" . | nindent 8 }}
      containers:
      - name: operator
        command: ["/bin/bash"]
        {{- if and .Values.encrypt .Values.encrypt.crypto .Values.encrypt.crypto.args .Values.encrypt.crypto.args.operatorDeployment }}
        args: ["-c", {{ .Values.encrypt.crypto.args.operatorDeployment | quote }}]
        {{- else }}
        args: ["-c", "/app/micro-gateway-operator --config=/app/config.yaml"]
        {{- end }}
        env: {{ include "apigw-helm-stacks.yaml" .Values.operator.extraEnvVars | nindent 10 }}
          - name: BK_GATEWAY_POD_IP
            valueFrom:
              fieldRef:
                fieldPath: status.podIPs
          - name: BK_GATEWAY_POD_NAME
            valueFrom:
              fieldRef:
                fieldPath: metadata.name
          - name: BK_GATEWAY_POD_NAMESPACE
            valueFrom:
              fieldRef:
                fieldPath: metadata.namespace
        image: {{ include "apigw-helm-stacks.image" (list  .Values.operator.image .Values.global) }}
        imagePullPolicy: {{ include "apigw-helm-stacks.image-pull-policy" .Values.operator.image }}
        ports:
          - name: http
            containerPort: {{ .Values.operator.service.httpPort }}
            protocol: TCP
        livenessProbe:
          httpGet:
            path: /ping
            port: http
          initialDelaySeconds: {{ .Values.operator.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.operator.livenessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.operator.livenessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.operator.livenessProbe.successThreshold }}
          failureThreshold: {{ .Values.operator.livenessProbe.failureThreshold }}
        readinessProbe:
          httpGet:
            path: /healthz
            port: http
          initialDelaySeconds: {{ .Values.operator.readinessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.operator.readinessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.operator.readinessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.operator.readinessProbe.successThreshold }}
          failureThreshold: {{ .Values.operator.readinessProbe.failureThreshold }}
        resources: {{ include "apigw-helm-stacks.container-resources" .Values.operator | nindent 10 }}
        volumeMounts: {{ include "apigw-helm-stacks.yaml" .Values.operator.extraVolumeMounts | nindent 10 }}
          - mountPath: /data/config/
            name: operator-config
          {{- if $apisixEtcd.tlsCertSecret }}
          - mountPath: {{ .Values.etcdCertPath.apisix }}
            name: etcd-apisix-certs
          {{- end }}
          {{- if $apigwEtcd.tlsCertSecret }}
          - mountPath: {{ .Values.etcdCertPath.operator }}
            name: etcd-operator-certs
          {{- end }}
          - name: config-volume
            mountPath: /app/config.yaml
            readOnly: true
            subPath: config.yaml
      restartPolicy: Always
      imagePullSecrets: {{ include "apigw-helm-stacks.image-pull-secrets" . | nindent 8 }}
      securityContext: {{ include "apigw-helm-stacks.container-security-context" . | nindent 8 }}
      volumes: {{ include "apigw-helm-stacks.yaml" .Values.operator.extraVolumes | nindent 8 }}
        - name: operator-config
          configMap:
            name: {{ include "apigw-helm-stacks.name-prefix" . }}-operator-config
            items:
            - key:  extra-resources.yaml
              path: extra-resources.yaml
        {{- if $apisixEtcd.tlsCertSecret }}
        - name: etcd-apisix-certs
          secret:
            secretName: {{ $apisixEtcd.tlsCertSecret }}
        {{- end }}
        {{- if $apigwEtcd.tlsCertSecret }}
        - name: etcd-operator-certs
          secret:
            secretName: {{ $apigwEtcd.tlsCertSecret }}
        {{- end }}
        - name: config-volume
          configMap:
            name: {{ include "apigw-helm-stacks.name-prefix" . }}-operator-config
            items:
            - key: config.yaml
              path: config.yaml
{{- end }}
