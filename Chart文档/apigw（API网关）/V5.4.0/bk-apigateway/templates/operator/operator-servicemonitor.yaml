{{- if and .Values.operator.enabled .Values.operator.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-operator
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels: {{ include "apigw-helm-stacks.labels" . | nindent 6 }}
      app.kubernetes.io/component: "operator"
  endpoints:
    - port: http
      path: "/metrics"
      interval: "{{ .Values.operator.serviceMonitor.interval }}"
      scrapeTimeout: "{{ .Values.operator.serviceMonitor.scrapeTimeout }}"
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
{{- end }}
