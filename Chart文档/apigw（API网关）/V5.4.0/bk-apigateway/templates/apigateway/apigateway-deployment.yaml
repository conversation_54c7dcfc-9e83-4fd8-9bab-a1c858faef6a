{{- if .Values.apigateway.enabled }}
{{- $apisixEtcd := fromYaml (include "apigw-helm-stacks.etcd.apisix" .) -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "apigw-helm-stacks.apigateway.name" . }}
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
    {{ if .Values.apigateway.rollout.enabled -}}
    rollout: "true"
    {{- end }}
spec:
  replicas: {{ include "apigw-helm-stacks.deployment-replicas" (list .Values.apigateway .Values) }}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 0
  selector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "apigateway"
  template:
    metadata:
      annotations:
        checksum/configmap: {{ include (print $.Template.BasePath "/apigateway/apigateway-configmap.yaml") . | sha256sum }}
        checksum/debug-configmap: {{ include (print $.Template.BasePath "/apigateway/apigateway-debug-configmap.yaml") . | sha256sum }}
        {{- if $apisixEtcd.tlsCertSecret }}
        checksum/etcd-apisix-secret: {{ include (print $.Template.BasePath "/basic/etcd-apisix-secret.yaml") . | sha256sum }}
        {{- end }}
        {{- if .Values.podAnnotations  }}
        {{- include "apigw-helm-stacks.pod-annotations" . | nindent 8 }}
        {{- end }}
      labels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 8 }}
        app.kubernetes.io/component: "apigateway"
    spec:
      terminationGracePeriodSeconds: 360
      serviceAccountName: {{ include "apigw-helm-stacks.service-account-name" . }}
      affinity: {{ include "apigw-helm-stacks.affinity" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      tolerations: {{ include "apigw-helm-stacks.tolerations" . | nindent 8 }}
      hostAliases: {{ include "apigw-helm-stacks.host-aliases" . | nindent 8 }}
      initContainers:
        {{- include "apigw-helm-stacks.wait-for-storages-job-init-container" . | nindent 8 }}
        {{- if .Values.operator.enabled }}
        {{- include "apigw-helm-stacks.wait-for-service-init-container" (list . (printf "%s-operator" (include "apigw-helm-stacks.name-prefix" .))) | nindent 8 }}
        {{- end }}
      containers:
      - command: ["sh"]
        {{- if and .Values.encrypt .Values.encrypt.crypto .Values.encrypt.crypto.args .Values.encrypt.crypto.args.apigatewayDeployment }}
        args: ["-c", {{ .Values.encrypt.crypto.args.apigatewayDeployment | quote }}]
        {{- else }}
        args: ["/data/bkgateway/bin/apisix-start.sh"]
        {{- end }}
        image: {{ include "apigw-helm-stacks.image" (list  .Values.apigateway.image .Values.global) }}
        imagePullPolicy: {{ include "apigw-helm-stacks.image-pull-policy" .Values.apigateway.image }}
        lifecycle:
          preStop:
            exec:
              command:
                - /bin/sh
                - -c
                - "sleep 120 && apisix quit"
        {{- if .Values.apigateway.livenessProbe.enabled }}
        livenessProbe:
          tcpSocket:
            port: {{ .Values.apigateway.service.httpPort }}
          initialDelaySeconds: {{ .Values.apigateway.livenessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.apigateway.livenessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.apigateway.livenessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.apigateway.livenessProbe.successThreshold }}
          failureThreshold: {{ .Values.apigateway.livenessProbe.failureThreshold }}
        {{- end }}
        {{- if .Values.apigateway.readinessProbe.enabled }}
        readinessProbe:
          httpGet:
            path: /healthz
            port: http
          initialDelaySeconds: {{ .Values.apigateway.readinessProbe.initialDelaySeconds }}
          periodSeconds: {{ .Values.apigateway.readinessProbe.periodSeconds }}
          timeoutSeconds: {{ .Values.apigateway.readinessProbe.timeoutSeconds }}
          successThreshold: {{ .Values.apigateway.readinessProbe.successThreshold }}
          failureThreshold: {{ .Values.apigateway.readinessProbe.failureThreshold }}
        {{- end }}
        name: apisix
        env: {{ include "apigw-helm-stacks.yaml" .Values.apigateway.extraEnvVars | nindent 8 }}
        - name: apisixDebugConfigPath
          value: /data/bkgateway/apisix-debug-config
        ports:
          - name: https
            containerPort: {{ .Values.apigateway.service.httpsPort }}
            protocol: TCP
          - name: http
            containerPort: {{ .Values.apigateway.service.httpPort }}
            protocol: TCP
          - name: metric
            containerPort: {{ .Values.apigateway.service.metricPort }}
            protocol: TCP
          - name: control
            containerPort: {{ .Values.apigateway.service.controlPort }}
            protocol: TCP
        resources: {{ include "apigw-helm-stacks.container-resources" .Values.apigateway | nindent 10 }}
        volumeMounts: {{ include "apigw-helm-stacks.yaml" .Values.apigateway.extraVolumeMounts | nindent 8 }}
        - mountPath: /data/bkgateway/apisix-debug-config
          name: apigateway-debug-config
        - mountPath: /usr/local/apisix/conf/config-encrypt.yaml
          name: apigateway-config
          subPath: config-encrypt.yaml
        {{- if $apisixEtcd.tlsCertSecret }}
        - name: etcd-apisix-certs
          mountPath: {{ .Values.etcdCertPath.apisix }}
        {{- end }}
      imagePullSecrets: {{ include "apigw-helm-stacks.image-pull-secrets" . | nindent 8 }}
      securityContext: {{ include "apigw-helm-stacks.container-security-context" . | nindent 8 }}
      restartPolicy: Always
      volumes: {{ include "apigw-helm-stacks.yaml" .Values.apigateway.extraVolumes | nindent 6 }}
      - name: apigateway-config
        configMap:
          name: {{ include "apigw-helm-stacks.name-prefix" . }}-apisix-config
          items:
          - key: config-encrypt.yaml
            path: config-encrypt.yaml
      - name: apigateway-debug-config
        configMap:
          name: {{ include "apigw-helm-stacks.name-prefix" . }}-apisix-debug-config
          items:
          - key: debug.yaml
            path: debug.yaml
      {{- if $apisixEtcd.tlsCertSecret }}
      - name: etcd-apisix-certs
        secret:
          secretName: {{ $apisixEtcd.tlsCertSecret }}
      {{- end }}
{{- end }}
