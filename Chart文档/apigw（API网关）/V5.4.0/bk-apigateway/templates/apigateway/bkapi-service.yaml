{{- if and .Values.apigateway.enabled .Values.apigateway.bkapiServiceName }}
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.apigateway.bkapiServiceName }}
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.apigateway.ingress.annotations "context" $) | nindent 4 }}
    "helm.sh/resource-policy": keep
spec:
  type: {{ .Values.apigateway.bkapiServiceType }}
  selector:
    app.kubernetes.io/name: {{ include "apigw-helm-stacks.name" . }}
    app.kubernetes.io/component: "apigateway"
  ports: {{ include "apigw-helm-stacks.yaml" .Values.apigateway.extraServicePorts | nindent 4 }}
    - name: http
      port: 80
      protocol: TCP
      targetPort: http
    - name: https
      port: 443
      protocol: TCP
      targetPort: https
{{- end }}
