{{- if and .Values.apigateway.enabled .Values.apigateway.rollout.enabled -}}
apiVersion: tkex.tencent.com/v1alpha1
kind: Rollout
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-apigateway-rollout
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  failurePolicy: {{ .Values.apigateway.rollout.failurePolicy }}
  objectRef:
    workloadRef:
      apiVersion: apps/v1
      kind: Deployment
      name: {{ include "apigw-helm-stacks.apigateway.name" . }}
  paused: {{ .Values.apigateway.rollout.paused | quote }}
  steps: {{ toYaml .Values.apigateway.rollout.steps | nindent 4 }}
{{- end }}