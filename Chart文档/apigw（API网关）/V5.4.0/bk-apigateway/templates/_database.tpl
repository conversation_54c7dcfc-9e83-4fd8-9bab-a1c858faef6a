{{/*
内建数据库名称
*/}}
{{- define "apigw-helm-stacks.mariadbName" -}}
{{- include "mariadb.primary.fullname" (dict "Values" .Values.mariadb "Chart" .Chart "Release" .Release) -}}
{{- end -}}

{{/*
默认数据库基础配置，处理了内建和外部数据库场景
*/}}
{{- define "apigw-helm-stacks.database" -}}
{{- $root := first . -}}
{{- $name := last . -}}
{{- $values := $root.Values -}}
{{- $db := index $values.externalDatabase $name -}}

{{- if $values.mariadb.enabled -}}
name: {{ $db.name | default "" }}
user: {{ $values.mariadb.auth.username | required "mariadb.auth.username is required" }}
password: {{ $values.mariadb.auth.password | required "mariadb.auth.password is required" }}
host: {{ include "apigw-helm-stacks.mariadbName" $root }}
port: {{ $values.mariadb.primary.service.port | required "mariadb.primary.service.port is required" }}

{{- else -}}
{{- $dbDefault := $values.externalDatabase.default -}}
name: {{ $db.name | default "" }}
user: {{ $db.user | default $dbDefault.user | required (printf "externalDatabase.%s.user is required" $name) }}
password: {{ $db.password | default $dbDefault.password | required (printf "externalDatabase.%s.password is required" $name) }}
host: {{ $db.host | default $dbDefault.host | required (printf "externalDatabase.%s.host is required" $name) }}
port: {{ $db.port | default $dbDefault.port | required (printf "externalDatabase.%s.port is required" $name) }}
{{- end -}}
{{- end -}}

{{/*
apigw 数据库配置，处理默认数据库合并逻辑
*/}}
{{- define "apigw-helm-stacks.databaseApigw" -}}
{{ include "apigw-helm-stacks.database" (list . "apigw") }}
{{- end -}}

{{/*
esb 数据库配置，处理默认数据库合并逻辑
*/}}
{{- define "apigw-helm-stacks.databaseEsb" -}}
{{ include "apigw-helm-stacks.database" (list . "esb") }}
{{- end -}}

{{/*
legacyEsb 数据库配置，处理默认数据库合并逻辑
*/}}
{{- define "apigw-helm-stacks.database.legacy-esb" -}}
{{- $db := .Values.externalDatabase.legacyEsb -}}
{{- $dbDefault := .Values.externalDatabase.default -}}

name: {{ $db.name | required "externalDatabase.legacyEsb.name is required" }}
user: {{ $db.user | default $dbDefault.user | required "externalDatabase.legacyEsb.user is required" }}
password: {{ $db.password | default $dbDefault.password }}
host: {{ $db.host | default $dbDefault.host }}
port: {{ $db.port | default $dbDefault.port | required "externalDatabase.legacyEsb.port is required" }}
{{- end -}}
