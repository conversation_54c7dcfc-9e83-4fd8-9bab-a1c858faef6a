{{/*
内建 Etcd 名称
*/}}
{{- define "apigw-helm-stacks.etcdName" -}}
{{- include "common.names.fullname" (dict "Values" .Values.etcd "Chart" .Chart "Release" .Release) -}}
{{- end -}}

{{/*
operator/dashboard Etcd 证书 Secret 名，若未启用 Etcd Client TLS 则返回空
*/}}
{{- define "apigw-helm-stacks.operatorEtcdCertSecretName" -}}
{{- if or .Values.etcd.enabled .Values.externalEtcd.operator.tls -}}
{{- print "bk-apigateway-operator-etcd-certs" -}}
{{- else if .Values.externalEtcd.operator.tlsExsitedSecret -}}
{{- .Values.externalEtcd.operator.tlsExsitedSecret -}}
{{- else -}}
{{- print "" -}}
{{- end -}}
{{- end -}}

{{/*
apisix Etcd 证书 Secret 名，若未启用 Etcd Client TLS 则返回空
*/}}
{{- define "apigw-helm-stacks.apisixEtcdCertSecretName" -}}
{{- if or .Values.etcd.enabled .Values.externalEtcd.apisix.tls -}}
{{- print "bk-apigateway-apisix-etcd-certs" -}}
{{- else if .Values.externalEtcd.apisix.tlsExsitedSecret -}}
{{- .Values.externalEtcd.apisix.tlsExsitedSecret -}}
{{- else -}}
{{- print "" -}}
{{- end -}}
{{- end -}}

{{/*
默认 Etcd 基础配置，处理了内建和外部 Etcd 场景
*/}}
{{- define "apigw-helm-stacks.etcd" -}}
{{- $root := first . -}}
{{- $name := last . -}}
{{- $values := $root.Values -}}
{{- $etcd := index $values.externalEtcd $name -}}

{{- if and (eq $name "apisix") $values.apisixResourceVersion -}}
prefix: {{ $values.etcdPrefix }}{{ $values.apisixResourceVersion }}-{{ $name }}
{{- else -}}
prefix: {{ $values.etcdPrefix }}{{ $name }}
{{- end -}}

{{/*
# 内建
*/}}
{{ if $values.etcd.enabled }}
host: {{ include "apigw-helm-stacks.etcdName" $root }}
port: {{ $values.etcd.service.ports.client }}
scheme: http
username: root
password: {{ $values.etcd.auth.rbac.rootPassword }}

# 外部
{{ else }}
{{ $etcdDefault := $values.externalEtcd.default }}
{{ $username := $etcd.username | default $etcdDefault.username }}
{{ $password := $etcd.password | default $etcdDefault.password }}
{{ $tlsCertSecret := $etcd.tls.existingSecret | default $etcdDefault.existingSecret }}
{{ $caCertFileName := $etcd.tls.caCertFileName | default $etcdDefault.tls.caCertFileName }}
{{ $certFileName := $etcd.tls.certFileName | default $etcdDefault.tls.certFileName }}
{{ $keyFileName := $etcd.tls.keyFileName | default $etcdDefault.tls.keyFileName }}
{{ $certBase64Encoded := $etcd.tls.certBase64Encoded | default $etcdDefault.tls.certBase64Encoded }}

host: {{ $etcd.host | default $etcdDefault.host }}
port: {{ $etcd.port | default $etcdDefault.port }}
# 外部证书
{{ if $tlsCertSecret }}
tlsCertSecret: {{ $tlsCertSecret }}
scheme: https

# 内置证书
{{ else if $certBase64Encoded }}
tlsCertSecret: {{ include "apigw-helm-stacks.name-prefix" $root }}-etcd-{{ $name }}-certs
scheme: https
certBase64Encoded: {{ $certBase64Encoded }}
keyBase64Encoded: {{ $etcd.tls.keyBase64Encoded | default $etcdDefault.tls.keyBase64Encoded | required (printf "externalEtcd.tls.keyBase64Encoded is required" $name) }}
caBase64Encoded: {{ $etcd.tls.caBase64Encoded | default $etcdDefault.tls.caBase64Encoded | required (printf "externalEtcd.tls.caBase64Encoded is required" $name) }}

# 用户名密码
{{ else if $username }}
username: {{ $username }}
password: {{ $password }}
scheme: http

{{ else }}
{{ fail (printf "auth config in externalEtcd.%s is required" $name) }}

{{ end }}

caCertFileName: {{ $caCertFileName }}
certFileName: {{ $certFileName }}
keyFileName: {{ $keyFileName }}

{{- end -}}
{{- end -}}


{{/*
apigw Etcd 配置，处理默认 Etcd 合并逻辑
*/}}
{{- define "apigw-helm-stacks.etcd.apigw" -}}
{{ include "apigw-helm-stacks.etcd" (list . "apigw") }}
{{- end -}}

{{/*
apisix Etcd 配置，处理默认 Etcd 合并逻辑
*/}}
{{- define "apigw-helm-stacks.etcd.apisix" -}}
{{ include "apigw-helm-stacks.etcd" (list . "apisix") }}
{{- end -}}
