{{- if and .Values.bkEsb.enabled .Values.bkEsb.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-bk-esb
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels: {{ include "apigw-helm-stacks.labels" . | nindent 6 }}
      app.kubernetes.io/component: "bk-esb"
  endpoints:
    - port: http
      path: "/metrics"
      interval: "{{ .Values.bkEsb.serviceMonitor.interval }}"
      scrapeTimeout: "{{ .Values.bkEsb.serviceMonitor.scrapeTimeout }}"
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
{{- end }}