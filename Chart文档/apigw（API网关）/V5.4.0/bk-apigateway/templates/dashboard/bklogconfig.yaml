{{- if and .Values.dashboard.enabled .Values.dashboard.bkLogConfig.enabled }}
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-dashboard-stdout
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  dataId: {{ .Values.dashboard.bkLogConfig.stdoutDataId }}
  encoding: {{ .Values.dashboard.bkLogConfig.stdoutEncoding }}
  logConfigType: std_log_config
  namespace: {{ .Release.Namespace }}
  labelSelector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "dashboard"
---
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-dashboard-celery-stdout
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  dataId: {{ .Values.dashboard.bkLogConfig.stdoutDataId }}
  encoding: {{ .Values.dashboard.bkLogConfig.stdoutEncoding }}
  logConfigType: std_log_config
  namespace: {{ .Release.Namespace }}
  labelSelector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "dashboard-celery"
---
apiVersion: bk.tencent.com/v1alpha1
kind: BkLogConfig
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-dashboard-beat-stdout
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  dataId: {{ .Values.dashboard.bkLogConfig.stdoutDataId }}
  encoding: {{ .Values.dashboard.bkLogConfig.stdoutEncoding }}
  logConfigType: std_log_config
  namespace: {{ .Release.Namespace }}
  labelSelector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "dashboard-beat"
{{- end }}
