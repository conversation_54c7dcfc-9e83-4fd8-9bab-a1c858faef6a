{{- if and .Values.dashboard.enabled .Values.dashboard.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-dashboard
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels: {{ include "apigw-helm-stacks.labels" . | nindent 6 }}
      app.kubernetes.io/component: "dashboard"
  endpoints:
    - port: http
      path: "/metrics"
      interval: "{{ .Values.dashboard.serviceMonitor.interval }}"
      scrapeTimeout: "{{ .Values.dashboard.serviceMonitor.scrapeTimeout }}"
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
{{- end }}