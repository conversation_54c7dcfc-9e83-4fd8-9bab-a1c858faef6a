{{- if and .Values.dashboard.enabled .Values.dashboard.ingress.enabled }}
{{- $backend := dict "context" . }}
{{- $_ := set $backend "serviceName" (printf "%s-dashboard" (include "apigw-helm-stacks.name-prefix" .)) }}
{{- $_ := set $backend "servicePort" .Values.dashboard.service.httpPort }}
apiVersion: {{ include "common.capabilities.ingress.apiVersion" . }}
kind: Ingress
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-dashboard
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.dashboard.ingress.annotations "context" $) | nindent 4 }}
spec: {{ include "apigw-helm-stacks.ingress-class" . | nindent 2 }}
  rules:
  - host: {{ include "apigw-helm-stacks.ingress-host" (list .Values.dashboard.ingress .Values.global) }}
    http:
      paths:
      - backend: {{ include "common.ingress.backend" $backend | nindent 10 }}
        path: {{ .Values.dashboard.ingress.path }}
        {{- if eq "true" (include "common.ingress.supportsPathType" .) }}
        pathType: Prefix
        {{- end }}
      {{- if and .Values.dashboardFe.enabled .Values.dashboardFe.ingress.enabled }}
      - backend: {{ include "apigw-helm-stacks.dashboard-fe.ingress-backend" . | nindent 10 }}
        path: {{ .Values.dashboardFe.ingress.path }}
        {{- if eq "true" (include "common.ingress.supportsPathType" .) }}
        pathType: Prefix
        {{- end }}
      {{- end }}
{{- end }}
