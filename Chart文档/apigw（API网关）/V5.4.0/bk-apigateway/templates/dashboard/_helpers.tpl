{{/*
etcd env
*/}}
{{- define "apigw-helm-stacks.dashboard.envs" -}}
{{- $apigwEtcd := fromYaml (include "apigw-helm-stacks.etcd.apigw" .) -}}
{{ include "apigw-helm-stacks.yaml" .Values.dashboard.extraEnvVars }}
- name: BK_ETCD_HOST
  value: "{{ $apigwEtcd.host }}"
- name: BK_ETCD_PORT
  value: "{{ $apigwEtcd.port }}"
- name: BK_GATEWAY_ETCD_NAMESPACE_PREFIX
  value: "{{ $apigwEtcd.prefix }}"

# feature flags
- name: FEATURE_FLAG_MICRO_GATEWAY_ENABLED
  value: "{{ .Values.dashboard.featureFlags.microGatewayEnabled }}"
# end

{{- if $apigwEtcd.tlsCertSecret }}
- name: BK_ETCD_CA_PATH
  value: "/etc/ssl/etcd/ssl/{{ $apigwEtcd.caCertFileName }}"
- name: BK_ETCD_CERT_PATH
  value: "/etc/ssl/etcd/ssl/{{ $apigwEtcd.certFileName }}"
- name: BK_ETCD_KEY_PATH
  value: "/etc/ssl/etcd/ssl/{{ $apigwEtcd.keyFileName }}"

{{- else }}
- name: BK_ETCD_USER
  value: "{{ $apigwEtcd.username }}"
- name: BK_ETCD_PASSWORD
  value: "{{ $apigwEtcd.password }}"
{{- end }}

{{- with index .Values.apigateway.pluginMetadata "bk-concurrency-limit" }}
- name: GATEWAY_CONCURRENCY_LIMIT_CONN
  value: "{{ .conn }}"
- name: GATEWAY_CONCURRENCY_LIMIT_BURST
  value: "{{ .burst }}"
- name: GATEWAY_CONCURRENCY_LIMIT_DEFAULT_CONN_DELAY
  value: "{{ .defaultConnDelay }}"
{{- end }}

{{- with index .Values.apigateway.pluginMetadata "bk-real-ip" }}
- name: GATEWAY_REAL_IP_RECURSIVE
  value: "{{ .recursive }}"
- name: GATEWAY_REAL_IP_SOURCE
  value: "{{ .source }}"
- name: GATEWAY_REAL_IP_TRUSTED_ADDRESSES
  value: "{{ .trustedAddresses | join "," }}"
{{- end }}

{{- with index .Values.apigateway.pluginMetadata "bk-opentelemetry" }}
- name: GATEWAY_OTEL_SAMPLER_NAME
  value: "{{ .sampler }}"
- name: GATEWAY_OTEL_ROOT_SAMPLER_RATIO
  value: "{{ .samplerRatio }}"
{{- end }}

{{- end }}

{{/*
Pod volumes
*/}}
{{- define "apigw-helm-stacks.dashboard.volumes" -}}
{{- $apigwEtcd := fromYaml (include "apigw-helm-stacks.etcd.apigw" .) -}}
{{- if $apigwEtcd.tlsCertSecret }}
- name: etcd-certs
  secret:
    secretName: {{ $apigwEtcd.tlsCertSecret }}
{{- end }}
{{ include "apigw-helm-stacks.yaml" .Values.dashboard.extraVolumes }}
{{- end }}

{{/*
Pod volume mounts
*/}}
{{- define "apigw-helm-stacks.dashboard.volume-mounts" -}}
{{- $apigwEtcd := fromYaml (include "apigw-helm-stacks.etcd.apigw" .) -}}
{{- if $apigwEtcd.tlsCertSecret }}
- name: etcd-certs
  mountPath: /etc/ssl/etcd/ssl/
{{- end }}
{{ include "apigw-helm-stacks.yaml" .Values.dashboard.extraVolumeMounts }}
{{- end }}
