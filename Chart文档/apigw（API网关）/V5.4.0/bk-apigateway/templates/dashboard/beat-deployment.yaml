{{- if .Values.dashboard.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "apigw-helm-stacks.name-prefix" . }}-dashboard-beat
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 0
  selector:
    matchLabels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 6 }}
      app.kubernetes.io/component: "dashboard-beat"
  template:
    metadata:
      annotations:
        checksum/configmap: {{ include (print $.Template.BasePath "/basic/env-configmap.yaml") . | sha256sum }}
        checksum/secret: {{ include (print $.Template.BasePath "/basic/encrypt-key-secret.yaml") . | sha256sum }}
        {{- if .Values.podAnnotations  }}
        {{- include "apigw-helm-stacks.pod-annotations" . | nindent 8 }}
        {{- end }}
      labels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 8 }}
        app.kubernetes.io/component: "dashboard-beat"
    spec:
      serviceAccountName: {{ include "apigw-helm-stacks.service-account-name" . }}
      affinity: {{ include "apigw-helm-stacks.affinity" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      tolerations: {{ include "apigw-helm-stacks.tolerations" . | nindent 8 }}
      hostAliases: {{ include "apigw-helm-stacks.host-aliases" . | nindent 8 }}
      initContainers: {{ include "apigw-helm-stacks.wait-for-migrations-init-container" .| nindent 8 }}
      containers:
      - command:
        - bash
        args:
        - /app/bin/start_beat.sh
        env: {{ include "apigw-helm-stacks.dashboard.envs" . | nindent 10 }}
        envFrom:
        - configMapRef:
            name: {{ include "apigw-helm-stacks.name-prefix" . }}-basic-env
        - secretRef:
            name: {{ include "apigw-helm-stacks.name-prefix" . }}-encrypt-key
        image: {{ include "apigw-helm-stacks.image" (list  .Values.dashboard.image .Values.global) }}
        imagePullPolicy: {{ include "apigw-helm-stacks.image-pull-policy" .Values.dashboard.image }}
        name: bk-apigateway-dashboard-beat
        resources: {{ include "apigw-helm-stacks.container-resources" .Values.dashboard | nindent 10 }}
        volumeMounts: {{- include "apigw-helm-stacks.dashboard.volume-mounts" . | nindent 10 }}
      restartPolicy: Always
      imagePullSecrets: {{ include "apigw-helm-stacks.image-pull-secrets" . | nindent 8 }}
      securityContext: {{ include "apigw-helm-stacks.container-security-context" . | nindent 8 }}
      volumes: {{- include "apigw-helm-stacks.dashboard.volumes" . | nindent 8 }}
{{- end }}