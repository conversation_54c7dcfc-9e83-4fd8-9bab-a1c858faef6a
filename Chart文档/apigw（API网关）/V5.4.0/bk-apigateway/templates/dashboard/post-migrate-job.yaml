{{- if .Values.dashboard.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "apigw-helm-stacks.job-name" (list . "dashboard-post-migrate") }}
  labels: {{ include "apigw-helm-stacks.labels" . | nindent 4 }}
spec:
  backoffLimit: 10
  parallelism: 1
  template:
    metadata:
      annotations: {{ include "apigw-helm-stacks.pod-annotations" . | nindent 8 }}
      labels: {{ include "apigw-helm-stacks.selector-labels" . | nindent 8 }}
        app.kubernetes.io/component: "dashboard-post-migrate"
    spec:
      serviceAccountName: {{ include "apigw-helm-stacks.service-account-name" . }}
      affinity: {{ include "apigw-helm-stacks.affinity" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      tolerations: {{ include "apigw-helm-stacks.tolerations" . | nindent 8 }}
      hostAliases: {{ include "apigw-helm-stacks.host-aliases" . | nindent 8 }}
      initContainers:
        {{- include "apigw-helm-stacks.wait-for-service-init-container" (list . (printf "%s-dashboard" (include "apigw-helm-stacks.name-prefix" .))) | nindent 8 }}
        {{- if .Values.bkEsb.enabled }}
        {{- include "apigw-helm-stacks.wait-for-job-init-container" (list . "bk-esb-migrate") | nindent 8 }}
        {{- include "apigw-helm-stacks.wait-for-service-init-container" (list . (printf "%s-bk-esb" (include "apigw-helm-stacks.name-prefix" .))) | nindent 8 }}
        {{- end }}
      containers:
      - command:
        - bash
        args:
        - /app/bin/post_migrate
        env: {{- include "apigw-helm-stacks.dashboard.envs" . | nindent 10 }}
        envFrom:
        - configMapRef:
            name: {{ include "apigw-helm-stacks.name-prefix" . }}-basic-env
        - secretRef:
            name: {{ include "apigw-helm-stacks.name-prefix" . }}-encrypt-key
        image: {{ include "apigw-helm-stacks.image" (list .Values.dashboard.image .Values.global) }}
        imagePullPolicy: {{ include "apigw-helm-stacks.image-pull-policy" .Values.dashboard.image }}
        name: bk-apigateway-dashboard-post-migrate
        resources: {{ include "apigw-helm-stacks.container-resources" .Values.dashboard | nindent 10 }}
        volumeMounts: {{- include "apigw-helm-stacks.dashboard.volume-mounts" . | nindent 10 }}
      imagePullSecrets: {{ include "apigw-helm-stacks.image-pull-secrets" . | nindent 8 }}
      nodeSelector: {{ include "apigw-helm-stacks.node-selector" . | nindent 8 }}
      restartPolicy: Never
      securityContext: {{ include "apigw-helm-stacks.container-security-context" . | nindent 8 }}
      volumes: {{- include "apigw-helm-stacks.dashboard.volumes" . | nindent 8 }}
  ttlSecondsAfterFinished: 86400
{{- end }}