# BK-APIGateway

BK-APIGateway 是由蓝鲸 PaaS 平台开发的 API 网关服务，主要提供请求代理、请求策略控制等功能。本文档内容为如何在 Kubernetes 集群上部署 BK-APIGateway 服务。

## API Gateway Helm 部署项目

### 子服务介绍

蓝鲸 API Gateway 主要由 4 个核心子服务组成。

| 名称             | 代号         | 描述                                                                              |
| ---------------- | ------------ | --------------------------------------------------------------------------------- |
| 网关服务         | apigateway   | 网关服务是 API Gateway 的核心服务，为所有用户提供请求代理、请求策略控制等功能     |
| 网关管理         | dashboard    | 网关管理后端服务，为网关管理员提供创建、修改网关、注册资源等功能，仅包含 REST API |
| 网关管理前端     | dashboard-fe | 网关管理前端，通过 REST API 与后端交互                                            |
| 新版编码组件服务 | bk-esb       | 支持通过编码方式，提供组件 API                                                    |

### 架构图

![arch](./arch.png)

## 部署

### 准备服务依赖

开始部署前，请准备好一套 Kubernetes 集群（版本 1.12 或更高），并安装 Helm 命令行工具（3.0 或更高版本）。

> 注：如使用 BCS 容器服务部署，可用 BCS 的图形化 Helm 功能替代 Helm 命令行。

#### 数据存储

以下为 BK-APIGateway 必须使用的数据存储服务：

- MySQL：用于存储关系数据，要求版本为 `5.7` 或更高；
- Redis：用于保存缓存数据，提升网关性能；

> 注：你可以选择自己搭建，或者直接从云计算厂商处购买这些服务，只要能保证从集群内能正常访问即可。

**注意，内置的存储依赖仅为方便开发联调而提供，默认开启，生产环境务必关闭。**

| 存储    | 控制变量        | 类型 | 默认值 |
| ------- | --------------- | ---- | ------ |
| mariadb | mariadb.enabled | bool | true   |
| redis   | reids.enabled   | bool | true   |

#### 其他服务

要正常运行 BK-APIGateway，除了准备核心的数据存储外，还需要用到一些蓝鲸体系内的其他服务，它们是：

- 蓝鲸 BKAuth：依赖其 API 接口校验应用
- 蓝鲸 Login：依赖其 API 接口校验用户

除以上服务外，还有其他几个依赖服务，如不提供也不会影响程序主要功能，但部分非核心功能可能会不可用：

- 蓝鲸 PaaS3：依赖其 API 接口
  - 如缺失，网关的“主动授权”等功能将无法正常使用
- 蓝鲸 BCS 容器服务（版本 1.3.25 以上）：用于简化部署
  - 如缺失，对程序功能没有任何影响

准备好依赖服务后，下一步是编写 `values.yaml` 配置文件。

### 准备 `values.yaml`

BK-APIGateway 无法直接通过 Chart 所提供的默认 `values.yaml` 完成部署，在执行 `helm install` 安装服务前，你必须按以下步骤准备好匹配当前部署环境的 `values.yaml`。

#### 1. 配置镜像地址

编写配置文件的第一步，是将 `global.imageRegistry` 配置为你所使用的镜像源地址。然后，再确认每个模块所使用镜像 tag 是否正确。

##### `values.yaml` 配置示例

```yaml
global:
  # 请保证服务相关的容器镜像已上传至该 registry 中
  imageRegistry: "mirrors.example.com"
...
apigateway:
  image:
    repository: "blueking/apigateway"
    tag: "1.0.0"
dashboard:
  image:
    repository: "blueking/apigateway-dashboard"
    tag: "1.0.0"
dashboardFe:
  image:
    repository: "blueking/apigateway-dashboard-fe"
    tag: "1.0.0"
bkEsb: ...
```

> 注：假如服务镜像需凭证才能拉取。请将对应密钥名称写入配置文件中，详细请查看 `global.imagePullSecrets` 配置项说明。

#### 2. 配置数据加密密钥

BK-APIGateway 服务使用对称加密算法保障敏感配置与服务数据安全。要启用加密功能，你首先得创建一个独一无二的密钥。该密钥内容为 **长度为 32 的字符串（需 base64 编码）**。

在 Linux 系统中，你可执行以下命令生成一个随机密钥：

```bash
tr -dc A-Za-z0-9 </dev/urandom | head -c 32 | base64
```

或者调用 Python 命令：

```bash
python -c 'import random, string, base64; s = "".join(random.choice(string.ascii_letters + string.digits) for _ in range(32)); print(base64.b64encode(s.encode()).decode())'
```

拿到密钥后，下一步是将其放入 `encryptKey` 配置项中。

注意事项：

- 密钥一旦生成并配置好以后，不可修改，否则会导致数据异常；
- 为了你的数据安全，请不要将密钥泄露给其他人。

`values.yaml` 配置示例

```yaml
encryptKey: "b3BmRmpwYWNoZH..."
```

#### 3. 初始化与配置数据存储

准备好服务所依赖的存储后，必须完成以下初始化操作：

**MySQL**
使用内置存储可跳过 1-2 步骤。

1. 创建普通用户 `bk_apigateway`；
2. 创建数据库 `bk_apigateway` 和 `bk_esb`，授予用户 `bk_apigateway` 访问数据库的所有权限；

**Redis**
使用内置存储可跳过。

1. 使用 redis-cli 命令测试 Redis 可正常连接使用

##### 填写数据存储配置

```yaml
externalDatabase:
  apigw:
    name: "bk_apigateway"
    host: "mysql.example.com"
    port: 3306
    user: "bk_apigateway"
    password: "xxxxxx"
  esb:
    name: "bk_esb"
    host: "mysql.example.com"
    port: 3306
    user: "bk_apigateway"
    password: "xxxxxx"
```

`values.yaml` 配置示例

```yaml
externalDatabase:
  apigw:
    name: "bk_apigateway"
    host: "mysql.example.com"
    port: 3306
    user: "bk_apigateway"
    password: "xxxxxx"
  esb:
    name: "bk_esb"
    host: "mysql.example.com"
    port: 3306
    user: "bk_apigateway"
    password: "xxxxxx"
  legacyEsb:
    name: "open_paas"
    host: ""
    port: 3306
    user: ""
    password: ""

## `externalRedis` 部分配置请参考对应配置项详细说明。
externalRedis:
  default:
    ....
```

#### 4. 配置 PaaS2 服务依赖

为了让 BK-APIGateway 正常工作，各子服务需要使用蓝鲸的应用身份（包括 AppCode  和 AppSecret）进行标识。该应用身份除起到标识作用外，还会在调用蓝鲸其它服务时用于鉴权，比如访问 BKAuth 接口。

需创建或获取以下蓝鲸应用账号：

- `bk_apigateway`，供 API Gateway 服务使用
- `bk_apigw_test`：供 API Gateway 的“在线调试”功能使用
- `bk_paas`：供 ESB 使用，BKAuth 部署时已创建

其中，`bk_apigateway` 需要被添加到“ESB 免用户认证应用白名单”中。

> 注：请使用 BKAuth 项目提供的功能，创建并获取应用账号。

创建并获取应用账号后，将各应用的 app secret 按照以下对应关系，配置到 `values.yaml` 中：

| 应用          | 配置                         |
| ------------- | ---------------------------- |
| bk_apigatway  | keys.apigatewayAppSecret     |
| bk_apigw_test | keys.apigatewayTestAppSecret |
| bk_paas       | keys.paas2AppSecret          |

`values.yaml` 配置示例

```yaml
keys:
  apigatewayAppSecret: "hOWXV1MTZ..."
  apigatewayTestAppSecret: "RCqd3TW3Bv..."
  paas2AppSecret: "feI28IwIz..."
```

#### 5. 配置服务访问域名

BK-APIGateway 服务的主要访问地址，由 `global.bkDomain` 配置项所控制。为了简化配置，此处采用了“配置一个根域，发散多个子域”的方式。

举例来说，如果 `global.bkDomain` 值为 `example.com`，那么网关主要的访问域名为：

- 云 API 访问域名：`bkapi.example.com`
- API 网关主站域名：`apigw.example.com`

为了让服务能被正常访问，以上域名需要在 DNS 服务中被配置为“指向 Ingress Controller 所在 IP”。如果这些域名并非真实存在，那你就得用一些其他手段来确保域名被正常解析。

举例来说，对于访问 APIGateway 的本地浏览器和其他客户端，你得修改系统的 `/etc/hosts` 文件，添加对应域名的指向记录，如：

```plain
## 将 127.0.0.1 替换为 Ingress Controller 的可访问 IP
127.0.0.1 bkapi.example.com
127.0.0.1 apigw.example.com
```

除此之外，部署在集群内的各个子模块之间，也会通过这些域名来互相调用对方。若集群内无法解析这些域名，可修改集群内的 DNS 服务，添加对应解析记录，或者设置 `global.HostAliases` 配置，具体可参考：[Adding entries to Pod /etc/hosts with HostAliases](https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/)

`values.yaml` 配置示例

```yaml
global:
  bkDomain: "example.com"
```

#### 6. 其他外部依赖

BK-APIGateway 服务在运行过程中，需要涉及许多其他服务。请配置以下外部依赖地址：

- 蓝鲸登录服务接口地址：`bkLoginApiUrl`
- BKAuth 服务接口地址：`bkAuthApiUrl`
- 蓝鲸文档中心地址：`bkDocsCenterUrl`

`values.yaml` 配置示例

```yaml
bkLoginApiUrl: ...
bkAuthApiUrl: ...
bkDocsCenterUrl: ...
```

> 注意：若集群内无法解析 bkLoginApiUrl、bkAuthApiUrl 配置的域名，可修改集群内的 DNS 服务，或设置 `global.HostAliases` 配置。

#### 7. 处理 ESB 专用配置

ESB 专用配置，主要用于启用接入 ESB 的 CC、JOB 等组件系统。可先跳过此部分配置，若需使用 ESB 中 CC、JOB 等系统的组件，按需添加对应系统的配置。
**注意，设置后，如果希望全量切换为 BK-ESB，请参考“常见问题：如何切换 ESB 访问流量”，进行切换。**

##### 配置 ESB 对接系统地址

ESB 作为服务总线，对接了许多蓝鲸体系下的其他子系统。要通过 ESB 访问这些系统的组件，你必须配置各子系统的真实接口地址。

有关各子系统的详细说明，请参考 values.yaml 中的样例

`values.yaml` 配置示例

```yaml
bkCmdbApiUrl: ...
bkJobApiBackendUrl: ...
bkUserApiUrl: ...
...
```

> 注意：若集群内无法解析配置的地址，可修改集群内的 DNS 服务，或设置 `global.HostAliases` 配置。

可将项目内的脚本 generate_esb_external_dependencies_conf.sh 拷贝到 PaaS2 的 ESB 项目下，执行以下指令，获取 ESB 的外部依赖地址。

- 注意事项：若获取的地址为 consul 域名，需替换为集群内可访问的域名

```shell
workon open_paas-esb

chmod +x generate_esb_external_dependencies_conf.sh
bash generate_esb_external_dependencies_conf.sh 2>/dev/null
```

##### 配置组件系统证书

若访问 JOB、GSE 系统的组件，需配置这两个系统的证书。
证书文件由蓝鲸官方提供，可使用以下命令，将证书文件转换为配置所需的文本内容：

```shell
cat <THE_CERT> | base64 -w 0
```

`values.yaml` 配置示例

```yaml
bkEsb:
  certs:
    - name: job_esb_api_client.crt
      base64Encoded: ...
    - name: job_esb_api_client.key
      base64Encoded: ...
    ...
```

#### 8. 设置 bkrepo 源

可选，用于将网关 SDK 上传到 bkrepo，方便 paas 部署的应用使用。
需要依赖 paas 创建的项目及账号，可直接复制 paas 对应的 values 值。
默认情况下，改功能不启用，请设置 `bkrepoConfig.enabled` 为 `true` 后启用。

`values.yaml` 配置示例

```yaml
bkrepoConfig:
  enabled: true
  endpoint: "http://bkrepo.example.com"
  bkpaas3Project: "bkpaas"
  bkpaas3Username: "bkpaas3"
  bkpaas3Password: "blueking"
  pypiRepository: "pypi"
```

#### 9. 蓝鲸调用链 APM

在蓝鲸监控创建 APM 应用，并将应用信息填入 `global.trace.otlp`。

- 因 apigateway 模块仅支持 http/otlp，因此需填入 http 协议地址

```yaml
global:
  trace:
    enabled: false
    otlp:
      host: 127.0.0.1
      port: 4318
      # 对应蓝鲸监控 SecureKey
      token: ""
      type: http

dashboard:
  trace:
    serviceName: "bk-apigateway-dashboard"
    sampler: "always_on"
    instrument:
      celery: true
      dbApi: false
      redis: false
```

#### 安装 Chart

完成 `values.yaml` 的所有准备工作后，要安装 BK-APIGateway，你必须先添加一个有效的 Helm repo 仓库。

```shell
## 请将 `<HELM_REPO_URL>` 替换为本 Chart 所在的 Helm 仓库地址
$ helm repo add bkee <HELM_REPO_URL>
```

添加仓库成功后，执行以下命令，在集群内安装名为 `bk-apigateway` 的 Helm release（使用默认项目配置）：

```shell
helm install bk-apigateway bkee/bk-apigateway -f values.yaml
```

上述命令将使用默认配置在 Kubernetes 集群中部署 bk-apigateway, 并输出访问指引。

#### 卸载 Chart

使用以下命令卸载`bk-apigateway`:

```shell
helm uninstall bk-apigateway
```

上述命令将移除所有与 bk-apigateway 相关的 Kubernetes 组件，并删除 release。

### 配置说明

以下为可配置的参数列表以及默认值 (也可直接参考 values.yaml 中的说明及默认值)

| 参数 | 类型 | 默认值  | 描述        |
|-----|------|---------|-------------|
| affinity | object | `{}` | 亲和性配置 |
| apigateway.adminApiKey | string | `""` | 网关模块，APISIX 管理密码 |
| apigateway.apisixConfig.bkAuthAuthorizationKeys | list | `- "app_code"等` | 网关票据关键字列表，不需要改 |
| apigateway.apisixConfig.bkAuthSensitiveKeys | list| `- "access_token"` |  网关敏感关键字列表，不需要改|
| apigateway.apisixConfig.luaSharedDict | object | `{}` |  |
| apigateway.apisixConfig.customLuaSharedDict | object | `{}` |  |
| apigateway.apisixConfig.hosts | object | `{}` |  |
| apigateway.apisixDebug.enabled | bool | `false` | 是否开启 apisix 的动态调试  |
| apigateway.apisixDebug.hookPhase | object | `{}` | 配置调试启用的 phase |
| apigateway.appendPluginAttrs | object | `{}` |  |
| apigateway.appendPlugins | list | `[]` |  |
| apigateway.appendStreamPlugins | list | `[]` |  |
| apigateway.bkLogConfig.containerDataId | int | `0` |  |
| apigateway.bkLogConfig.enabled | bool | `false` |  |
| apigateway.bkLogConfig.fileDataId | int | `0` |  |
| apigateway.bkLogConfig.stdoutDataId | int | `0` |  |
| apigateway.enabled | bool | `true` |  |
| apigateway.extraApisixConfig | object | `{}` |  |
| apigateway.extraNginxConfig | object | `{}` |  |
| apigateway.extraServicePorts | string | `nil` |  |
| apigateway.extraEnvVars | list | `[]` |  |
| apigateway.image.pullPolicy | string | `"IfNotPresent"` |  |
| apigateway.image.registry | string | `"hub.bktencent.com"` |  |
| apigateway.image.repository | string | `"blueking/bk-micro-gateway-apisix"` |  |
| apigateway.image.tag | string | `"0.3.0-alpha.3"` |  |
| apigateway.nginxConfig.httpClientMaxBodySize | string | `"40m"` |  |
| apigateway.nginxConfig.workerProcesses | int | `4` |  |
| apigateway.nginxConfig.errorLogLevel | string| `warn` | 可以调整日志级别，用于调试等场景 |
| apigateway.overridePlugins | list | `[]` |  |
| apigateway.overrideStreamPlugins | list | `[]` |  |
| apigateway.pluginMetadata.bk-concurrency-limit.burst | int | `1000` |  |
| apigateway.pluginMetadata.bk-concurrency-limit.conn | int | `2000` |  |
| apigateway.pluginMetadata.bk-concurrency-limit.defaultConnDelay | int | `1` |  |
| apigateway.pluginMetadata.bk-opentelemetry.sampler | string | `"parent_base"` |  |
| apigateway.pluginMetadata.bk-opentelemetry.samplerRatio | float | `0.001` |  |
| apigateway.pluginMetadata.bk-real-ip.recursive | bool | `true` |  |
| apigateway.pluginMetadata.bk-real-ip.source | string | `"http_x_forwarded_for"` |  |
| apigateway.pluginMetadata.bk-real-ip.trustedAddresses[0] | string | `"127.0.0.1"` |  |
| apigateway.pluginMetadata.bk-real-ip.trustedAddresses[1] | string | `"::1"` |  |
| apigateway.rollout.enabled | bool | `false` |  |
| apigateway.rollout.failurePolicy | string | `"Backward"` |  |
| apigateway.rollout.paused | string | `"false"` |  |
| apigateway.rollout.steps[].pause.duration | string | `"60s"` | 恢复过程暂停时间  |
| apigateway.rollout.steps[].replicas | int | `1` | 灰度副本数 |
| apigatewayCore.bkLogConfig.containerDataId | int | `0` |  网关 core service 模块 |
| apigatewayCore.bkLogConfig.enabled | bool | `false` |  |
| apigatewayCore.bkLogConfig.fileDataId | int | `0` |  |
| apigatewayCore.bkLogConfig.stdoutDataId | int | `0` |  |
| apigatewayCore.debug | bool | `true` |  |
| apigatewayCore.enabled | bool | `true` |  |
| apigatewayCore.image.pullPolicy | string | `"IfNotPresent"` |  |
| apigatewayCore.image.registry | string | `"hub.bktencent.com"` |  |
| apigatewayCore.image.repository | string | `"blueking/bk-apigateway-core"` |  |
| apigatewayCore.image.tag | string | `"0.1.0-alpha.2"` |  |
| apigatewayCore.logLevel | string | `"debug"` |  |
| apisixResourceVersion | string | `""` |  |
| bkApigwDashboardApiUrl | string | `""` |  |
| bkPaas3Url | string | `""` |  |
| bkAuthApiUrl | string | `"http://bkauth"` | BKAuth API 地址 |
| bkBscpApiUrl | string | `"http://bk-bscp-api.example.com"` | bscp |
| bkCicdkitUrl | string | `"http://bk-cicdkit.example.com"` | cicdkit |
| bkCmdbApiUrl | string | `"http://bk-cmdb-api"` | 蓝鲸配置平台后台 API 地址 |
| bkDataBksqlUrl | string | `"http://bk-data-bksql.example.com"` | bk-data-bksql |
| bkDataModelflowUrl | string | `"http://bk-data-modelflow.example.com"` | bk-data-modelflow |
| bkDataProcessorapiUrl | string | `"http://bk-data-processorapi.example.com"` | bk-data-processorapi |
| bkDataUrl | string | `"http://bk-data.example.com"` | bk-data |
| bkDataV3AccessapiUrl | string | `"http://bkbase-datahubapi-api.bkbase.svc.cluster.local:8000"` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3AiopsapiUrl | string | `"http://bkbase-aiopsapi-api.bkbase.svc.cluster.local:8000"` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3AlgorithmapiUrl | string | `""` |计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace  |
| bkDataV3AuthapiUrl | string | `"http://bkbase-authapi-api.bkbase.svc.cluster.local:8000"` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3BksqlUrl | string | `"http://bkbase-bksqlextend-service.bkbase.svc.cluster.local:8596"` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3DatabusapiUrl | string | `"http://bkbase-datahubapi-api.bkbase.svc.cluster.local:8000"` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3DatacubeapiUrl | string | `""` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3DataflowapiUrl | string | `"http://bkbase-dataflowapi-api.bkbase.svc.cluster.local:8000"` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3DatalabapiUrl | string | `"http://bkbase-datalabapi-api.bkbase.svc.cluster.local:8000"` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3DatamanageapiUrl | string | `"http://bkbase-datamanageapi-api.bkbase.svc.cluster.local:8000"` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3DataqueryapiUrl | string | `"http://bkbase-queryengine-api.bkbase.svc.cluster.local:8000"` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3MetaapiUrl | string | `"http://bkbase-metaapi-api.bkbase.svc.cluster.local:8000"` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3ModelapiUrl | string | `""` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3QueryengineapiUrl | string | `"http://bkbase-queryengine-api.bkbase.svc.cluster.local:8000"` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3ResourcecenterapiUrl | string | `"http://bkbase-resourcecenterapi-api.bkbase.svc.cluster.local:8000"` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3StorekitapiUrl | string | `"http://bkbase-datahubapi-api.bkbase.svc.cluster.local:8000"` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDataV3LangserverUrl | string | `"http://bkbase-bksql-language-server.bkbase.svc.cluster.local:8000"` | 计算平台后台服务 API 地址，默认是固定 bkbase 的 namespace |
| bkDevopsUrl | string | `"http://bk-devops.example.com"` | devops (旧版) 地址 |
| bkDocsCenterApiUrl | string | `"http://apps.example.com/bk--docs--center"` | 蓝鲸文档中心后台 API 地址 |
| bkDocsCenterUrl | string | `"https://bk.tencent.com/docs"` | 蓝鲸文档中心 URL |
| bkEsb.bkLogConfig.containerDataId | int | `1` | ESB 模块 |
| bkEsb.bkLogConfig.enabled | bool | `false` |  |
| bkEsb.bkLogConfig.stdoutDataId | int | `1` |  |
| bkEsb.certs[].base64Encoded | string | `""` | 系统证书 |
| bkEsb.certs[].name | string | `"gseca.crt"` | 系统证书名 |
| bkEsb.customComponentConfigMaps | string | `nil` |  |
| bkEsb.enabled | bool | `true` |  |
| bkEsb.image.pullPolicy | string | `"IfNotPresent"` |  |
| bkEsb.image.registry | string | `"hub.bktencent.com"` |  |
| bkEsb.image.repository | string | `"blueking/apigateway-esb"` |  |
| bkEsb.image.tag | string | `"1.3.0-alpha.1"` |  |
| bkEsb.userVerificationExemptedApps | list | `[]` |  |
| bkFtaUrl | string | `"http://bk-fta.example.com"` | fta |
| bkGseCacheapiHost | string | `"bk-gse-api"` | 蓝鲸管控平台 cacheapi 模块 API 地址 |
| bkGseCacheapiPort | string | `"59313"` | 蓝鲸管控平台 cacheapi 模块 API 地址 |
| bkGseConfigUrl | string | `"http://bk-gse-config:59702"` | 蓝鲸管控平台配置后台 API 地址 |
| bkGsePmsUrl | string | `"http://bk-gse-procmgr:52030"` | 蓝鲸管控平台插件管理后台 API 地址 |
| bkGsekitUrl | string | `"http://apps.example.com/bk--gsekit"` | 蓝鲸 GSEKIT SaaS URL |
| bkIamSaasApiUrl | string | `"http://bkiam-saas-api"` | 蓝鲸权限中心 SaaS 的后台 API 地址 |
| bkItsmUrl | string | `"http://apps.example.com/bk--itsm"` |  蓝鲸流程管理 SaaS URL|
| bkJobApiBackendUrl | string | `"https://bk-job-gateway"` |  蓝鲸作业平台后台 API 访问地址|
| bkLogSearchApiUrl | string | `"http://bk-log-search-api"` | 蓝鲸日志平台后台 API URL |
| bkLoginApiUrl | string | `"http://bk-login-web"` |  蓝鲸登录后台的内部服务地址（一般用于校验登录 token）|
| bkLoginUrl | string | `"http://paas.example.com/login/"` | 蓝鲸 Login url（浏览器跳转登录用的 URL 前缀） |
| bkMonitorApiUrl | string | `"http://bk-monitor-api"` | 蓝鲸监控平台后台 API URL |
| bkNodemanApiUrl | string | `"http://apps.example.com/prod--backend--bk--nodeman"` | 蓝鲸节点管理 SaaS backend 模块 URL |
| bkSopsUrl | string | `"http://apps.example.com/prod--api--bk--sops"` | 蓝鲸标准运维 SaaS URL |
| bkSsmApiUrl | string | `"http://bkssm-web"` | 蓝鲸凭证管理后台 API 地址 |
| bkUserApiUrl | string | `"http://bkuserapi-web"` | 蓝鲸用户管理后台 API 地址 |
| bkrepoConfig.apigatewayPassword | string | `"blueking"` | bkrepo 配置 |
| bkrepoConfig.apigatewayProject | string | `"bk_apigateway"` |  |
| bkrepoConfig.apigatewayUsername | string | `"bk_apigateway"` |  |
| bkrepoConfig.bkpaas3Password | string | `"blueking"` |  |
| bkrepoConfig.bkpaas3Project | string | `"bkpaas"` |  |
| bkrepoConfig.bkpaas3Username | string | `"bkpaas3"` |  |
| bkrepoConfig.enabled | bool | `true` |  |
| bkrepoConfig.endpoint | string | `"http://bkrepo.example.com"` |  |
| bkrepoConfig.genericBucket | string | `"generic"` |  |
| bkrepoConfig.pypiRepository | string | `"pypi"` |  |
| dashboard.bkLogConfig.enabled | bool | `false` | 网关管理模块 |
| dashboard.bkLogConfig.stdoutDataId | int | `1` |  |
| dashboard.builtinGateway | string | `nil` |  |
| dashboard.enabled | bool | `true` |  |
| dashboard.featureFlags.microGatewayEnabled | bool | `false` |  |
| dashboard.image.pullPolicy | string | `"IfNotPresent"` |  |
| dashboard.image.registry | string | `"hub.bktencent.com"` |  |
| dashboard.image.repository | string | `"blueking/apigateway-dashboard"` |  |
| dashboard.image.tag | string | `"1.4.0-alpha.1"` |  |
| dashboard.jwtIssuer | string | `""` |  |
| dashboard.trace.instrument.celery | bool | `true` |  |
| dashboard.trace.instrument.dbApi | bool | `false` |  |
| dashboard.trace.instrument.redis | bool | `false` |  |
| dashboard.trace.sampler | string | `"always_on"` |  |
| dashboard.trace.serviceName | string | `"bk-apigateway-dashboard"` |  |
| dashboardFe.enabled | bool | `true` | 网关管理前端 |
| dashboardFe.image.pullPolicy | string | `"IfNotPresent"` |  |
| dashboardFe.image.registry | string | `"hub.bktencent.com"` |  |
| dashboardFe.image.repository | string | `"blueking/apigateway-dashboard-fe"` |  |
| dashboardFe.image.tag | string | `"1.2.0-alpha.1"` |  |
| debug | bool | `false` | 是否开启调试 |
| defaultMicroGatewayInstanceId | string | `""` | 默认微网关实例 ID |
| encryptKey | string | `""` | 加密密钥，敏感；配置后不可修改，否则会导致加密数据异常 |
| etcd.auth.rbac.allowNoneAuthentication | bool | `false` |  内建 ETCD |
| etcd.auth.rbac.create | bool | `true` |  |
| etcd.auth.rbac.rootPassword | string | `"blueking"` |  |
| etcd.auth.token.type | string | `"simple"` |  |
| etcd.enabled | bool | `true` |  |
| etcd.image.registry | string | `"docker.io"` |  |
| etcd.image.repository | string | `"bitnami/etcd"` |  |
| etcd.image.tag | string | `"3.5.7-debian-11-r20"` |  |
| etcd.nameOverride | string | `"etcd"` |  |
| etcd.persistence.enabled | bool | `true` |  |
| etcd.persistence.size | string | `"10Gi"` |  |
| etcd.service.ports.client | int | `2379` |  |
| etcdCertPath.apisix | string | `"/data/bkgateway/apisix-certs"` |  |
| etcdCertPath.operator | string | `"/data/bkgateway/operator-certs"` |  |
| etcdPrefix | string | `"/bk-gateway-"` |  |
| externalDatabase.apigw.host | string | `"mysql.example.com"` | 外部数据库配置，apigateway 数据库 |
| externalDatabase.apigw.name | string | `"bk_apigateway"` |  |
| externalDatabase.apigw.password | string | `""` |  |
| externalDatabase.apigw.port | int | `3306` |  |
| externalDatabase.apigw.user | string | `"bk_apigateway"` |  |
| externalDatabase.esb.host | string | `"mysql.example.com"` | 外部数据库配置，esb 数据库 |
| externalDatabase.esb.name | string | `"bk_esb"` |  |
| externalDatabase.esb.password | string | `""` |  |
| externalDatabase.esb.port | int | `3306` |  |
| externalDatabase.esb.user | string | `"bk_apigateway"` |  |
| externalDatabase.legacyEsb.host | string | `""` | 外部数据库配置，legacyEsb 数据库 |
| externalDatabase.legacyEsb.name | string | `"open_paas"` |  |
| externalDatabase.legacyEsb.password | string | `""` |  |
| externalDatabase.legacyEsb.port | int | `3306` |  |
| externalDatabase.legacyEsb.user | string | `"bk_apigateway"` |  |
| externalRabbitmq.default.host | string | `"rabbitmq.example.com"` |  外部 Rabbitmq 配置|
| externalRabbitmq.default.port | int | `5672` |  |
| externalRabbitmq.default.username | string | `"bk_apigateway"` |  |
| externalRabbitmq.default.password | string | `""` |  |
| externalRabbitmq.default.vhost | string | `"bk_apigateway"` |  |
| externalElasticsearch.default.host | string | `"elasticsearch.example.com"` |  外部 Elasticsearch 配置|
| externalElasticsearch.default.password | string | `""` |  |
| externalElasticsearch.default.port | int | `9200` |  |
| externalElasticsearch.default.user | string | `"bk_apigateway"` |  |
| externalEtcd.default.host | string | `"etcd.example.com"` | 外部 ETCD 配置 |
| externalEtcd.default.password | string | `""` |  |
| externalEtcd.default.port | int | `2379` |  |
| externalEtcd.default.tls.caBase64Encoded | string | `""` |  |
| externalEtcd.default.tls.caCertFileName | string | `"ca.crt"` |  |
| externalEtcd.default.tls.certBase64Encoded | string | `""` |  |
| externalEtcd.default.tls.certFileName | string | `"tls.crt"` |  |
| externalEtcd.default.tls.existingSecret | string | `""` |  |
| externalEtcd.default.tls.keyBase64Encoded | string | `""` |  |
| externalEtcd.default.tls.keyFileName | string | `"tls.key"` |  |
| externalEtcd.default.username | string | `""` |  |
| externalRedis.default.db | int | `0` | 外部 Redis |
| externalRedis.default.host | string | `"redis.example.com"` |  |
| externalRedis.default.password | string | `""` |  |
| externalRedis.default.poolSize | int | `300` |  |
| externalRedis.default.port | int | `6379` |  |
| fullnameOverride | string | `""` | 完整替代名称 |
| global.bkDomain | string | `"example.com"` | 根域名，将会根据这个值生成 Ingress 的 Host |
| global.bkDomainScheme | string | `"http"` | 域名协议，用于展示，不影响 ingress |
| global.hostAliases | list | `[]` | 域名别名 |
| global.imagePullSecrets | string | `nil` | 拉取镜像密钥 |
| global.imageRegistry | string | `""` | 源地址，优先级最高 |
| global.kubeVersion | string | `""` | Kubernetes 集群版本 |
| global.trace.enabled | bool | `false` | 蓝鲸调用链，是否开启 |
| global.trace.otlp.host | string | `"127.0.0.1"` | 蓝鲸调用链，服务地址 |
| global.trace.otlp.port | int | `4318` | 蓝鲸调用链，服务端口 |
| global.trace.otlp.token | string | `""` | 蓝鲸调用链，接口认证 token  |
| global.trace.otlp.type | string | `"http"` | 蓝鲸调用链，协议类型  |
| ingressClassName | string | `""` | Ingress 类名 |
| ipFamily | string | `"dualstack"` | 地址监听模式，可选项：ipv4、ipv6、dualstack |
| keys.apigatewayAppCode | string | `"bk_apigateway"` | 应用 ID |
| keys.apigatewayAppSecret | string | `""` | 应用密钥 |
| keys.apigatewayTestAppCode | string | `"bk_apigw_test"` | 网关测试应用的应用 ID，用于网关在线调试 |
| keys.apigatewayTestAppSecret | string | `""` | 网关测试应用的应用密钥，用于网关在线调试 |
| keys.paas2AppSecret | string | `""` | 应用 bk_paas 的密钥 |
| managers[0] | string | `"admin"` | 网关管理员 |
| mariadb.architecture | string | `"standalone"` | 内建数据库 |
| mariadb.auth.password | string | `"blueking"` |  |
| mariadb.auth.rootPassword | string | `"blueking"` |  |
| mariadb.auth.username | string | `"bk-apigateway"` |  |
| mariadb.enabled | bool | `true` |  |
| mariadb.image.registry | string | `"docker.io"` |  |
| mariadb.image.repository | string | `"bitnami/mariadb"` |  |
| mariadb.image.tag | string | `"10.5.11-debian-10-r34"` |  |
| mariadb.initdbScriptsConfigMap | string | `"bk-apigateway-init-sql"` |  |
| mariadb.nameOverride | string | `"mariadb"` |  |
| mariadb.primary.persistence.enabled | bool | `true` |  |
| mariadb.primary.persistence.size | string | `"10Gi"` |  |
| mariadb.primary.service.port | int | `3306` |  |
| nameOverride | string | `""` | 覆盖默认名称（替代名称 RELEASE-NAME 前缀） |
| nodeSelector | object | `{}` | 节点选择器 |
| operator.bkLogConfig.enabled | bool | `false` | operator 模块 |
| operator.bkLogConfig.stdoutDataId | int | `1` |  |
| operator.enabled | bool | `true` |  |
| operator.extraArgs.apisix-resource-flush-mode | string | `"diff"` |  |
| operator.extraArgs.apisix-resource-writer | string | `"etcd"` |  |
| operator.extraRoutes | list | `[]` |  |
| operator.image.pullPolicy | string | `"IfNotPresent"` |  |
| operator.image.registry | string | `"hub.bktencent.com"` |  |
| operator.image.repository | string | `"blueking/bk-micro-gateway-operator"` |  |
| operator.image.tag | string | `"0.3.0-alpha.3"` |  |
| podAnnotations | object | `{}` | pod 注解 |
| preferIpv6 | bool | `false` | 优先使用何种 IP 协议。 |
| rbac.create | bool | `true` | 访问控制，是否创建 |
| redis.architecture | string | `"standalone"` |  内建 Redis|
| redis.auth.password | string | `"blueking"` |  |
| redis.enabled | bool | `true` |  |
| redis.image.registry | string | `"docker.io"` |  |
| redis.image.repository | string | `"bitnami/redis"` |  |
| redis.image.tag | string | `"6.2.5-debian-10-r11"` |  |
| redis.master.persistence.enabled | bool | `false` |  |
| redis.master.service.port | int | `6379` |  |
| redis.nameOverride | string | `"redis"` |  |
| redis.replica.persistence.enabled | bool | `false` |  |
| redis.replica.replicaCount | int | `1` |  |
| registerCrds | bool | `true` |  是否创建网关 CRD |
| replicaCount | int | `1` | 默认进程副本数 |
| securityContext | string | `nil` | 容器安全性 |
| sentryDsn | string | `""` | 启用 sentry |
| serviceAccount.create | bool | `true` | 服务账户，是否创建 |
| serviceAccount.name | string | `""` | 服务账户，名称 |
| tolerations | list | `[]` | 污点配置 |

#### 如何修改配置项

在安装 Chart 时，你可以通过 `--set key=value[,key=value]` 的方式，在命令参数里修改各配置项。例如：

```shell
helm install bk-apigateway bkee/bk-apigateway --set global.bkDomain="example.com"
```

此外，你也可以把所有配置项写在 YAML 文件（常被称为 Helm values 文件）里，通过 `-f` 指定该文件来使用特定配置项：

```shell
helm install bk-apigateway bkee/bk-apigateway -f values.yaml
```

执行 `helm show values`，你可以查看 Chart 的所有默认配置：

```shell
## 查看默认配置
helm show values bkee/bk-apigateway

## 保存默认配置到文件 values.yaml
helm show values bkee/bk-apigateway > values.yaml
```

### 常见问题

#### 1. 如何切换 ESB 访问流量

可以通过 Nginx 将 paas2 esb 请求切换到 bk-esb 中，但需要保证 bk-esb 的域名能合法解析（安装后通过 helm note 提供）。
在 paas2 机器上设置 consul 配置：

```shell
consul kv put "bkcfg/fqdn/bk-esb" "<bkapi_domain>"
```

设置后需要重新渲染 Nginx 配置：

```shell
systemctl restart consul-template
```

如需切换回 paas2 esb，执行以下操作即可：

```shell
consul kv delete "bkcfg/fqdn/bk-esb"
```

#### 2. 如何导入自定义编码组件

请参照文档：[组件编码](https://bk.tencent.com/docs/document/6.0/130/5928) 开发系统代码。
进入自定义组件根目录，有类似的目录结构：

```plain
components/generic/apis/
└── hcp
    ├── __init__.py
    ├── get_host_list.py
    └── toolkit
        ├── __init__.py
        ├── configs.py
        └── tools.py
```

使用 chart 包自带的 esb-custom-component-configmap-updater.sh 脚本，执行以下命令：
  esb-custom-component-configmap-updater.sh -n bk-apigateway -r path/to/components/generic/apis/
请修改命令中的命名空间和组件路径，指定你的组件目录，执行之后，命令会调用 kubectl 创建对应的 configmap，并输出对应的 values 配置：

```yaml
bkEsb:
  customComponentConfigMaps:
    - configmap: "esb.custom.hcp"
      mountPath: "hcp"
    - configmap: "esb.custom.hcp.toolkit"
      mountPath: "hcp/toolkit"
```

请将输出的 `bkEsb.customComponentConfigMaps` 合并到 values，重新更新 chart。
configmap 更新之后，esb 不会自动重启，需要重新发布生效。

#### 3. 如何给用户添加权限访问“组件管理”功能

通过 kubectl 查找 dashboard 模块 pod，然后进入该 pod

```bash
kubectl get pods | grep bk-apigateway-dashboard

kubectl exec -it bk-apigateway-dashboard-xxxxxxxxx-yyyyy -- sh
```

执行以下命令，进入 django shell

```bash
python manage.py shell
```

在 django shell 中，执行以下代码给用户添加权限

```bash
from django.contrib.auth import get_user_model

user, _ = get_user_model().objects.get_or_create(username="your username")
user.is_active = True
user.is_staff = True
user.is_superuser = True
user.save()
```

#### 4. 金丝雀发布

Chart 版本（0.5.6+）提供了金丝雀发布特性，以降低发布风险，提高服务可用性。

目前，支持两种金丝雀发布方案：

- 单 Release 发布，基于 BCS Rollout，支持灰度网关服务 apigateway
- 双 Release 发布，基于 Ingress Nginx，支持按流量比例、按用户灰度

##### 4.1 基于 BCS Rollout 的单 Release 发布

此方案，通过控制网关服务 apigateway pod 创建的数量，支持网关服务 apigateway 的灰度，以提高网关核心服务的可用性。

前置条件：

- 容器集群需安装 BCS Rollout 插件

升级步骤：

1. 启用金丝雀发布

在准备的 `values.yaml` 中，添加如下配置：

```yaml
apigateway:
  rollout:
    # 设置 enabled=true，启用金丝雀发布
    enabled: true
    # steps 配置可选，如未设置，将使用默认配置
    # 指定发布过程，可以配置多个发布步骤，每个步骤可以配置参数：副本数、暂停
    steps:
      - # 灰度副本数，可以填百分比，也可以填具体数字
        replicas: 1
        # 可以填写 duration 暂停指定时间，也可以填写为 "{}"，指定永久暂停直至手动介入
        pause: {}
      - replicas: 50%
        pause:
          duration: 60s
```

2. 发布 chart

Release 第一次启用金丝雀发布时，rollout steps 中灰度步骤不生效，将会直接发布网关服务 apigateway。

3. 金丝雀发布过程

金丝雀发布时，如步骤 1 中的配置，发布过程将进行：

- apigateway deploy 的更新将被暂停，deploy-canary 被建立
- deploy-canary 副本数为 1，然后永久暂停
- 手动将 rollout 的 spec.paused 改为 "false"，继续发布
- deploy-canary 副本数扩容为原 deploy 的 50%，然后暂停 60s
- 开始更新原 deploy，等到原 deploy 所有 pod 都 ready 后，再销毁 deploy-canary
- 灰度结束

参考指令：

```bash
# 修改 rollout
kubectl edit rollout -n <namespace>

# 查看 rollout 过程中状态
kubectl get rollout -w -n <namespace>
```

##### 4.2 基于 Ingress Nginx 的双 Release 发布

双 Release 发布，允许在同一个命名空间中部署多个 Release，并可通过 Ingress Nginx 控制转发流量比例，避免停机升级，提高服务可用性。

注意事项：

- 存储组件必须使用外部存储
- Ingress Controller 默认只支持 Nginx 来实现流量按比例转发

升级步骤：

1. 假设已部署 Release 的名称为 bk-apigateway, 则新部署金丝雀 Release 的名称可设置为 bk-apigateway-canary

2. 准备金丝雀 Release bk-apigateway-canary 的发布配置：values-canary.yaml
   - Values `apigateway.bkapiServiceName` 设置为空
   - Values `registerCrds` 设置为 false
   - 给所有的 Ingress 加上 canary 注解，其中权重可以自行调整

示例如下：

```yaml
registerCrds: false

apigateway:
  bkapiServiceName: ""
  # 可通过 replicaCount 控制创建 Pod 个数
  replicaCount: 1
  ingress:
    annotations:
      # 标记此 Ingress 为金丝雀发布
      nginx.ingress.kubernetes.io/canary: "true"
      # API 服务，设置灰度比例，范围：0 ~ 100
      nginx.ingress.kubernetes.io/canary-weight: "10"

dashboard:
  ingress:
    annotations:
      nginx.ingress.kubernetes.io/canary: "true"
      # 前端项目，用户手动设置浏览器 Cookies：canary=always，则灰度此用户的请求
      nginx.ingress.kubernetes.io/canary-by-cookie: "canary"
      # 设置权重比例为 100，不灰度前端项目
      # nginx.ingress.kubernetes.io/canary-weight: "100"
```

3. 发布金丝雀 Release

```bash
# values.yaml 为正常发布的配置，values-canary.yaml 为金丝雀发布的特殊配置
helm install bk-apigateway-canary bkee/bk-apigateway -f values.yaml -f values-canary.yaml
```

4. 手动调整金丝雀 Release 中 Ingress 的灰度权重，逐步增加比例，例如：10 [-> 20] [-> 50] [-> 100]

```bash
kubectl edit ingress bk-apigateway-canary-apigateway
```

5. 更新原 Release。待原 Release 更新成功后，可将金丝雀 Release 中灰度权重调整为 0

6. 删除金丝雀 Release，灰度结束

```bash
helm uninstall bk-apigateway-canary
```

#### 5. 如何支持 IPv6 环境

在默认情况下，Chart 支持双栈环境，且倾向监听 IPv4 协议栈。

- 双栈环境：设置 `ipFamily=dualstack`，如需倾向监听 IPv6 协议栈，设置 `preferIpv6=true`；
- 单栈环境：设置 `ipFamily` 为真实的协议栈。

#### 6. 如何从 0.4.x 版本迁移

推荐先将当前的版本升级为 0.4.65+，可以考虑使用前面给出的金丝雀发布方案。
当前版本增加了 ETCD 依赖，请提前准备。

更新 Values：

- [必须] 将 etcd 信息配置到 externalEtcd 中；
- [推荐] 在 bkrepo 中创建名为 `bk_apigateway` 的项目和用户，将用户名密码更新到 `bkrepoConfig`，不配置将影响 SDK 功能；
- [推荐] 增加一个日志采集的 DataId，用于存储网关内部模块日志，配置到 `apigateway.bkLogConfig.fileDataId`，不配置会复用标准输出的 DataId；
- [推荐] 梳理访问链路的中间代理地址，将地址列表整理到 `apigateway.pluginMetadata["bk-real-ip"].trustedAddresses` 中，如不配置，将会影响 IP 访问控制插件和客户端 IP 获取的逻辑；

#### 7. 网关如何获取客户端 IP

网关依赖 X-Forwared-For 请求头来获取客户端 IP（当请求头不存在时，直接使用 TCP 源 IP），当 X-Forwared-For 有多个地址时，从右获取第一个不信任的地址作为客户端 IP，防止地址伪造，如：

假设 ******** 作为已知的代理机器被加入网关信任列表，当传入以下请求时：

X-Forwarded-For: ********, ********, ********

则认为客户端 IP 为 ********
因此，当 IP 访问控制功能工作不正确时，请检查访问链路中间的代理地址是否正确加入到信任列表中，通过 `apigateway.pluginMetadata["bk-real-ip"].trustedAddresses`。
默认的，网关只信任本机地址。

#### 8. apigateway 模块部署最佳实践

默认配置的 `apisix` 的 `processes=4`

所以生产环境建议配置为`limits.cpu = requests.cpu = 4`固定不修改，内存固定为`8G`

```yaml
apigateway:
  ## 资源配置
  ##
  resources:
    ## 资源限制
    ##
    limits:
      cpu: "4"
      memory: "8Gi"
    ## 资源请求
    ##
    requests:
      cpu: "4"
      memory: "8Gi"
```
