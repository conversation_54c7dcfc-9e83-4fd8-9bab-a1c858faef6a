# 版本历史

## 1.12.14
app version: 1.12.14

dashboard:
- 已申请权限列表中，添加网关ID，组件系统ID
- build(bkpkg-bk_apigateway.yaml): add bkpkg yaml
- feat(esb/dependencies.yaml): update to 2.14.62

apisix:
- refactor(bk-delete-sensitive): remove useless core.log.warn
- fix(bk-define): bka<PERSON> check verified_app_required should be true before check resource_perm_required
- fix(ci/dockerfile.apisix-test-busted): build image fail
- fix: fix bkauth request add request-id

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.14
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.7
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.9

## 1.12.13
app version: 1.12.13

dashboard:
- 查询应用已有权限时，允许展示正在申请中的权限状态

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.13
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.7
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.8

## 1.12.12
app version: 1.12.12

dashboard:
- 告警信息中，去掉querystring by @alex-smile in #404
- ESB 组件权限建单、查询，同网关 bk-esb 权限单、数据关联起来 by @alex-smile in #395

apisix:
- fix: remove useless core.log.warn

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.12
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.7
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.8

## 1.12.11
app version: 1.12.11

dashboard:
- 检查应用访问网关资源的权限，如果快过期则发送通知给应用负责人
- esb 更新到 2.14.61

core-api:
- gateway_name to gateway_id cache expire time to 1 min

operator:
- bugfix: delete gateway panic
- fix: disable version probe keepalive

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.11
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.7
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.7

## 1.12.10
app version: 1.12.10

dashboard:
- add auth_location to log_fields
- openapi sync stage 不触发发布，允许网关的环境中变量不存在
- 统计: 支持按网关拉取请求量数据

operator:
- 修复 goroutine 泄露
- 修复 watch bug, 会导致panic
- 调整同步

apisix:
- 允许不从请求参数获取认证参数
- fix(bk-auth-verify): add app_code/app_secret length check
- fix radixtree_uri_with_parameter match 404 if the param in uri is non-ASCII


- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.10
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.6
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.7


## 1.12.9
app version: 1.12.9

apisix 支持 graceful shutdown

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.9
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.5
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.6

## 1.12.8

app version: 1.12.8

apisix 支持 graceful shutdown

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.8
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.5
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.6

## 1.12.7

app version: 1.12.7

- esb:
  - use value in config_fields as component default config

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.7
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.4
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.5


## 1.12.6

app version: 1.12.6

- dashboard:
  - fix(apps/plugin): PluginConfigManager missing method bulk_delete

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.6
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.4
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.5

## 1.12.5

app version: 1.12.5

- dashboard:
  - fix: update esb gunicorn param: set keep-alive to 0
  - fix: add bk_token to api docs
- operator:
  - fix: apisix etcd got unused field: desc
  - fix: the operator probe is incorrect, should sleep for waitTime before check
- apisix:
  - fix(plugin/rate-limit): change redis connection pool_size from 100 to 75
  - fix(metrics): remove unused labels, make the metrics smaller

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.5
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.4
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.5

## 1.12.4

app version: 1.12.4

- fix: apisix rebuild tree
- fix: sentrylogs demonize

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.4
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.3
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.4

## 1.12.3

app version: 1.12.3

- bugfix: operator 修复 diff 失效
- bugfix: apisix 修复 dns 解析问题

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.3
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.2
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.3

## 1.12.2

app version: 1.12.2

- 网关支持配置开发者

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.2
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.1
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.1

## 1.12.1

app version: 1.12.1

- 修复 operator 去重 bug(打 tag, 版本规范化)

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.1
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.1
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.1

## 1.12.0

app version: 1.12.0

- 修复 operator 去重问题
- apisix 线上问题修复

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.0
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.0
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.0

## 1.12.0-beta.3

app version: 1.12.0-beta.3

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.0-beta.3
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.0-beta.1
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.0-beta.1

## v1.12.0-beta.2

app version: v1.12.0-beta.2

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.0-beta.2
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.0-beta.1
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.0-beta.1

## v1.12.0-beta.1

- operator 全量同步 bug

app version: v1.12.0-beta.1

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.0-beta.1
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.0-beta.1
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.0-beta.1

## v1.12.0-alpha.10

- 修复前端测试出的缺陷

app version: 1.12.0-alpha.10

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.0-alpha.9

## v1.12.0-alpha.9

- 修复前端测试出的缺陷

app version v1.12.0-alpha.9

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.0-alpha.8
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.0-alpha.5
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.0-alpha.7

## v1.12.0-alpha.8

- bugfix: resource header rewrite use same lrucache with stage header rewrite #40
- 请求后端接口异常时，不对结果进行缓存 #39

app version v1.12.0-alpha.8

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.0-alpha.7
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.0-alpha.5
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.0-alpha.6 (changed)

## v1.12.0-alpha.7

- operator 支持 trace
- 一些修复
- 前端修复无法点击的问题
- bugfix: header rewrite empty (#35)
- bugfix: 认证应用/用户时，如果是访问后端接口异常等错误，响应码改为 500 (#37)

app version v1.12.0-alpha.7

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.0-alpha.7
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.0-alpha.5
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.0-alpha.5

app version v1.12.0-alpha.6

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.0-alpha.6
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.0-alpha.5
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.0-alpha.4

## v1.12.0-alpha.6

只改了 chart version, app version 还是 v1.12.0-alpha.5

- 回滚 operator, tracing 会导致 operator 无法拉起

## v1.12.0-alpha.5

- bugfix

the release log:

- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.0-alpha.5
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.0-alpha.4
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.0-alpha.3

## v1.12.0-alpha.4

- bugfix: dashboard / plugin: cors / plugin: ip-restriction
- revert: apisix from 3.2.2 to 3.2.1

## v1.12.0-alpha.3

- bugfix: distribute() fail

## 1.12.0-alpah.2

- bugfix: apps/plugin django command migrate_header_rewrite fail; called in post_migrate

## 1.12.0-alpha.1

- 支持配置 externalRabbitmq
- dashboard post-migrate job 依赖 esb sevice 和 esb migrate job
- ingress 删除  `/docs/backend/`, 去除 api-support 遗留配置

the release log:
- https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.12.0-alpha.1
- https://github.com/TencentBlueKing/blueking-apigateway-apisix/releases/tag/v1.12.0-alpha.1
- https://github.com/TencentBlueKing/blueking-apigateway-operator/releases/tag/v1.12.0-alpha.1

-------------------

## 1.11.1

- 使用 concurrent_log_handler 代替 ConcurrentLogHandler

## 1.11.0

- 前端问题修复

https://github.com/TencentBlueKing/blueking-apigateway/releases/tag/v1.11.0

## 1.11.0-alpha.2

- 更新 Dashboard/ESB/Core-api 镜像 tag v1.11.0-alpha.2

## 1.11.0-alpha.1

- 1.11.0 提测版本

-------------------

## 1.10.0-beta.7

- bk-cors 插件，allow_origins、allow_origins_by_regex 只能一个有效

## 1.10.0-beta.6

- bk-cors 插件表单，部分字段添加正则校验

## 1.10.0-beta.5

- 修复添加 ESB 免用户认证应用白名单

## 1.10.0-beta.4

- bk-cors 插件表单，添加字段 allow_origins_by_regex，并添加一些插件配置的数据校验

## 1.10.0-beta.3

- values.yaml: add nginx config

## 1.10.0-beta.2

- esb: 更新配置类组件到版本 2.14.50
- esb: 修复组件 fe_list_users，添加组件 fe_update_user_language

## 1.10.0-beta.1

- 测试通过，版本晋级

## 1.10.0-alpha.2

- 优化前端体验

-------------------

## 1.0.0-alpha.4

- 合并 dashboard, esb 的 settings

## 1.0.0-alpha.3

- 拆分 dashboard, esb 的 start.sh；并将脚本挪到项目 bin 目录

-------------------

## 0.7.0-alpha.7

- esb 默认日志文件路径，调整为 /app/logs/
- dashboard, dashboard-fe, bk-esb, apigateway-core 统一版本号

## 0.7.0-alpha.5

- remove: chart-tests
- dashboard:
  - 重构 controller 中将网关数据写入 etcd 的功能
    - 如果资源、环境绑定了同一类型的访问策略、插件配置，则使用插件配置
    - 环境全局流量控制，如未启用，则不再将此插件写入 etcd
    - 优化网关发布的日志信息
  - 插件配置绑定时，主动触发信号，滚动更新发布网关
  - 微网关实例配置中 secret_key 不允许修改
  - 优化 command remove_invalid_stages_from_micro_gateway 的日志输出
  - 新增非公开表单插件：网关错误使用 HTTP 状态码 200(不推荐)
  - 网关 bk-esb "网关错误使用 HTTP 状态码 200(不推荐)"，使用插件配置代替访问策略
  - 添加插件绑定时新增校验，如果绑定对象已绑定同类型的访问策略，则不允许添加插件绑定
  - 访问 bkrepo 服务的 sdk 切换为 blue-krill
  - 登录模块依赖的 sdk bkpaas-auth 由 0.4.3 升级至 2.0.3；并去除对 SSM 服务的依赖
  - 新增 "backend/docs/" 开头的 urls，前端统一切换为此地址
  - [修复] 去除容器内产生的空日志文件
  - [修复] 插件配置列表中，允许展示已启用的、非公开插件的完整信息
  - [修复] 网关停用时，删除共享微网关实例中该网关的数据

## 0.7.0-alpha.3

- 重构：operator 去掉启动参数，使用 config.yml 启动
- bugfix:
  - operator 启动后等待 60s 检查 liveness
  - operator 以证书方式访问 apisix etcd 配置错误
  - apisix bk-components/apigateway-core 配置 url 错误
- 重构：合并 api-support-fe 到 dashboard-fe
- operator: 修复 路径匹配优先级问题

-------------------

## 0.7.x

- breaking changes: `externalRedis` 不再支持 sentinel 模式
- 更新：api-support-fe 合并到 dashboard-fe
- 更新：去除 bk-thunderstone 模块
- 新增：增加 apigateway-core 模块
- 重构：apigateway-dashboard 的 controller 模块
- 重构：operator

## 0.6.x

- 升级到 apisix 3.2

## 0.5.x

- 代码重构
