{{- if and .Values.saas.enabled .Values.saas.web .Values.saas.web.enabled -}}
{{- $fullName := printf "%s-%s" (include "bk-monitor.monitor.fullname" .) "web" -}}
{{- $process := .Values.saas.web -}}
apiVersion: {{ include "common.capabilities.deployment.apiVersion" . }}
kind: Deployment
metadata:
  name: {{ $fullName }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonLabels "context" $) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" $) | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels: {{- include "common.labels.matchLabels" . | nindent 6 }}
  replicas: {{ default 1 $process.replicaCount }}
  template:
    metadata:
      labels: {{- include "common.labels.standard" . | nindent 8 }}
      {{- if .Values.podLabels }}
        {{- include "common.tplvalues.render" (dict "value" .Values.podLabels "context" $) | nindent 8 }}
      {{- end }}
        process: web
        processType: web
      {{- if .Values.podAnnotations }}
      annotations:
        {{- include "common.tplvalues.render" (dict "value" .Values.podAnnotations "context" $) | nindent 8 }}
      {{- end }}
    spec:
      {{- include "bk-monitor.monitor.imagePullSecrets" . | nindent 6 }}
      {{- if .Values.hostAliases }}
      hostAliases: {{- include "common.tplvalues.render" (dict "value" .Values.hostAliases "context" $) | nindent 8 }}
      {{- end }}
      priorityClassName: {{ .Values.priorityClassName | quote }}
      {{- if .Values.podSecurityContext.enabled }}
      securityContext: {{- omit .Values.podSecurityContext "enabled" | toYaml | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bk-monitor.monitor.serviceAccountName" . }}
      {{- if .Values.affinity }}
      affinity: {{- include "common.tplvalues.render" (dict "value" .Values.affinity "context" $) | nindent 8 }}
      {{- else }}
      affinity:
        podAffinity: {{- include "common.affinities.pods" (dict "type" .Values.podAffinityPreset "context" $) | nindent 10 }}
        podAntiAffinity: {{- include "common.affinities.pods" (dict "type" .Values.podAntiAffinityPreset "context" $) | nindent 10 }}
        nodeAffinity: {{- include "common.affinities.nodes" (dict "type" .Values.nodeAffinityPreset.type "key" .Values.nodeAffinityPreset.key "values" .Values.nodeAffinityPreset.values) | nindent 10 }}
      {{- end }}
      nodeSelector: {{- include "common.tplvalues.render" (dict "value" .Values.nodeSelector "context" $) | nindent 8 }}
      {{- if .Values.tolerations }}
      tolerations: {{- include "common.tplvalues.render" (dict "value" .Values.tolerations "context" $) | nindent 8 }}
      {{- end }}
      initContainers:
        {{- if .Values.logDirectories.enabled }}
        {{- include "bk-monitor.monitor.logPermissionsInitContainer" (dict "serviceName" "web" "values" .Values) | nindent 8 }}
        {{- end }}
        - name: "check-migrate-job"
          image: {{ include "bk-monitor.monitor.k8sWaitFor.image" . }}
          imagePullPolicy: IfNotPresent
          args:
            - "job"
            - {{ include "bk-monitor.monitor.migrateJobName" . }}
      containers:
        - name: {{ $fullName }}
          image: {{ include "bk-monitor.monitor.image" . }}
          imagePullPolicy: {{ .Values.image.pullPolicy | quote }}
          {{- if .Values.containerSecurityContext.enabled }}
          securityContext: {{- omit .Values.containerSecurityContext "enabled" | toYaml | nindent 12 }}
          {{- end }}
          {{- if $process.command }}
          command: {{- include "common.tplvalues.render" (dict "value" $process.command "context" $) | nindent 12 }}
          {{- else }}
          command: ["/app/code/tini","--","/app/venv/bin/gunicorn", "wsgi", "-b", "[::]:8080", "-e", "DJANGO_SETTINGS_MODULE=settings", "--timeout", "300", "--workers=4", "--threads=8", "--worker-tmp-dir", "/dev/shm", "--max-requests", "1000"]
          {{- end }}
          {{- if $process.args }}
          args: {{- include "common.tplvalues.render" (dict "value" $process.args "context" $) | nindent 12 }}
          {{- end }}
          env:
            - name: DJANGO_CONF_MODULE
              value: conf.web.{{ include "bk-monitor.monitor.environment" . }}.{{ include "bk-monitor.monitor.platform" . }}
            {{- include "bk-monitor.monitor.builtInEnv" . | nindent 12 }}
            {{- if .Values.extraEnvVars }}
              {{- toYaml .Values.extraEnvVars | nindent 12 }}
            {{- end }}
            {{- if $process.extraEnvVars }}
              {{- include "common.tplvalues.render" (dict "value" $process.extraEnvVars "context" $) | nindent 12 }}
            {{- end }}
          envFrom:
            {{- if .Values.extraEnvVarsCM }}
            - configMapRef:
                name: {{ .Values.extraEnvVarsCM }}
            {{- end }}
            {{- if .Values.extraEnvVarsSecret }}
            - secretRef:
                name: {{ .Values.extraEnvVarsSecret }}
            {{- end }}
            {{- if $process.extraEnvVarsCM }}
            - configMapRef:
                name: {{ $process.extraEnvVarsCM }}
            {{- end }}
            {{- if $process.extraEnvVarsSecret }}
            - secretRef:
                name: {{ $process.extraEnvVarsSecret }}
            {{- end }}
          ports:
            - name: http
              containerPort: 8080
          livenessProbe:
            tcpSocket:
              port: http
          readinessProbe:
            httpGet:
              path: /api/status/business/
              port: http
          resources:
          {{- if $process.resources }}
          {{- toYaml $process.resources | nindent 12 }}
          {{- else }}
          {{- include "bk-monitor.monitor.defaultResources" . | nindent 12}}
          {{- end }}
          volumeMounts:
          {{- if $process.volumeMounts }}
          {{- toYaml $process.volumeMounts | nindent 12 }}
          {{- end }}
          {{- if .Values.logDirectories.enabled }}
          {{- include "bk-monitor.monitor.logVolumeMounts" (dict "serviceName" "web" "values" .Values) | nindent 12 }}
          {{- end }}
      volumes:
      {{- if $process.volumes }}
      {{- toYaml $process.volumes | nindent 8 }}
      {{- end }}
      {{- if .Values.logDirectories.enabled }}
      {{- include "bk-monitor.monitor.logVolumes" (dict "serviceName" "web" "values" .Values) | nindent 8 }}
      {{- end }}
{{- end }}
