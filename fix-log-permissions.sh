#!/bin/bash

# Script to fix bk-monitor log directory permissions
# This script should be run on all Kubernetes nodes where bk-monitor pods will be scheduled

set -e

LOG_BASE_DIR="/apps/logs/bkmonitor"
USER_ID=1001
GROUP_ID=1001

echo "Creating bk-monitor log directories with proper permissions..."

# Create base log directory
sudo mkdir -p "$LOG_BASE_DIR"

# List of all bk-monitor services that need log directories
SERVICES=(
    "alarm-access-data"
    "alarm-access-event-worker"
    "alarm-access-event"
    "alarm-access-real-time-data"
    "alarm-action-cron-worker"
    "alarm-action-worker"
    "alarm-alert-worker"
    "alarm-alert"
    "alarm-api-cron-worker"
    "alarm-beat"
    "alarm-composite"
    "alarm-converge-worker"
    "alarm-cron-worker"
    "alarm-detect"
    "alarm-fta-action-worker"
    "alarm-healthz"
    "alarm-image-worker"
    "alarm-long-task-cron-worker"
    "alarm-metadata-task-worker"
    "alarm-nodata"
    "alarm-report-cron-worker"
    "alarm-service-worker"
    "alarm-trigger"
    "alarm-webhook-action-worker"
    "api"
    "prom-agg-gateway"
    "web-beat"
    "web-celery-flower"
    "web-query-api"
    "web-worker-base"
    "web-worker-resource"
    "web-worker"
    "web"
)

# Create directories for each service
for service in "${SERVICES[@]}"; do
    service_dir="$LOG_BASE_DIR/$service"
    echo "Creating directory: $service_dir"
    sudo mkdir -p "$service_dir"
    sudo chown -R $USER_ID:$GROUP_ID "$service_dir"
    sudo chmod -R 755 "$service_dir"
done

# Set ownership and permissions for base directory
sudo chown -R $USER_ID:$GROUP_ID "$LOG_BASE_DIR"
sudo chmod -R 755 "$LOG_BASE_DIR"

echo "Log directory permissions fixed successfully!"
echo "Base directory: $LOG_BASE_DIR"
echo "Owner: $USER_ID:$GROUP_ID"
echo "Permissions: 755"
