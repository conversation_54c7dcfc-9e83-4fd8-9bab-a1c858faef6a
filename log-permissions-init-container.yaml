# Patch to add init container for fixing log directory permissions
# Apply this to your bk-monitor values.yaml or as a separate patch

# Add this to your monitor values.yaml under the monitor section:
monitor:
  # Enable volume permissions init container
  volumePermissions:
    enabled: true
    image:
      registry: docker.io
      repository: busybox
      tag: "1.35"
      pullPolicy: IfNotPresent
    # Security context for the init container (needs to run as root to change ownership)
    securityContext:
      runAsUser: 0
      runAsGroup: 0
    # Resources for the init container
    resources:
      limits:
        cpu: 100m
        memory: 128Mi
      requests:
        cpu: 50m
        memory: 64Mi

  # Update log directories configuration
  logDirectories:
    enabled: true
    globalPrefix: "/data/app/logs/bk_monitorv3"
    hostLogPath: "/apps/logs/bkmonitor"
    # Add user and group IDs for permission fixing
    userId: 1001
    groupId: 1001

---
# Alternative: Kubernetes Job to fix permissions on all nodes
apiVersion: batch/v1
kind: Job
metadata:
  name: fix-bkmonitor-log-permissions
  namespace: blueking
spec:
  template:
    spec:
      hostNetwork: true
      hostPID: true
      containers:
      - name: fix-permissions
        image: busybox:1.35
        command:
        - /bin/sh
        - -c
        - |
          # Create log directories with proper permissions
          mkdir -p /host/apps/logs/bkmonitor
          
          # List of services
          for service in alarm-access-data alarm-access-event-worker alarm-access-event alarm-access-real-time-data alarm-action-cron-worker alarm-action-worker alarm-alert-worker alarm-alert alarm-api-cron-worker alarm-beat alarm-composite alarm-converge-worker alarm-cron-worker alarm-detect alarm-fta-action-worker alarm-healthz alarm-image-worker alarm-long-task-cron-worker alarm-metadata-task-worker alarm-nodata alarm-report-cron-worker alarm-service-worker alarm-trigger alarm-webhook-action-worker api prom-agg-gateway web-beat web-celery-flower web-query-api web-worker-base web-worker-resource web-worker web; do
            mkdir -p /host/apps/logs/bkmonitor/$service
            chown -R 1001:1001 /host/apps/logs/bkmonitor/$service
            chmod -R 755 /host/apps/logs/bkmonitor/$service
          done
          
          # Set base directory permissions
          chown -R 1001:1001 /host/apps/logs/bkmonitor
          chmod -R 755 /host/apps/logs/bkmonitor
          
          echo "Log directory permissions fixed successfully!"
        securityContext:
          privileged: true
        volumeMounts:
        - name: host-root
          mountPath: /host
      volumes:
      - name: host-root
        hostPath:
          path: /
      restartPolicy: OnFailure
      nodeSelector:
        # Run on all nodes where bk-monitor might be scheduled
        kubernetes.io/os: linux
